// Cloudflare Workers 豆瓣推荐API
export async function onRequest(context) {
    const { request, env } = context;
    const url = new URL(request.url);
    
    // 设置CORS头
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
    };
    
    if (request.method === 'OPTIONS') {
        return new Response(null, { status: 200, headers: corsHeaders });
    }
    
    if (request.method !== 'GET') {
        return new Response(JSON.stringify({ error: '方法不被允许' }), {
            status: 405,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
    
    try {
        const type = url.searchParams.get('type');
        const tag = url.searchParams.get('tag');
        const pageStart = parseInt(url.searchParams.get('page_start')) || 0;
        const pageLimit = parseInt(url.searchParams.get('page_limit')) || 16;
        
        if (!type || !['movie', 'tv'].includes(type)) {
            return new Response(JSON.stringify({ error: '无效的类型参数，必须是movie或tv' }), {
                status: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
        if (!tag) {
            return new Response(JSON.stringify({ error: '缺少标签参数' }), {
                status: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
        // 限制分页参数范围
        if (pageStart < 0 || pageStart > 1000) {
            return new Response(JSON.stringify({ error: '无效的页面起始参数' }), {
                status: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }
        
        if (pageLimit < 1 || pageLimit > 50) {
            return new Response(JSON.stringify({ error: '无效的页面大小参数' }), {
                status: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        const cacheKey = `douban_recommend_${type}_${tag}_${pageStart}_${pageLimit}`;
        const CACHE_TTL = 60 * 60; // 1小时
        
        // 尝试从KV缓存获取
        let data = null;
        try {
            if (env.LIBRETV_PROXY_KV) {
                const cached = await env.LIBRETV_PROXY_KV.get(cacheKey);
                if (cached) {
                    data = JSON.parse(cached);
                }
            }
        } catch (e) {
        }
        
        if (!data) {
            // 缓存未命中，请求API
            const apiUrl = `https://movie.douban.com/j/search_subjects?type=${type}&tag=${encodeURIComponent(tag)}&sort=recommend&page_limit=${pageLimit}&page_start=${pageStart}`;
            
            try {
                const response = await fetch(apiUrl, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                        'Referer': 'https://movie.douban.com/',
                        'Accept': 'application/json, text/plain, */*',
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                data = await response.json();
            } catch (err) {
                
                // 备用方法
                const fallbackUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(apiUrl)}`;
                const fallbackResponse = await fetch(fallbackUrl);
                
                if (!fallbackResponse.ok) {
                    throw new Error(`备用API请求失败! 状态: ${fallbackResponse.status}`);
                }
                
                const fallbackData = await fallbackResponse.json();
                
                if (fallbackData && fallbackData.contents) {
                    data = JSON.parse(fallbackData.contents);
                } else {
                    throw new Error("无法获取有效数据");
                }
            }
            
            // 缓存结果
            try {
                if (env.LIBRETV_PROXY_KV) {
                    await env.LIBRETV_PROXY_KV.put(cacheKey, JSON.stringify(data), {
                        expirationTtl: CACHE_TTL
                    });
                }
            } catch (e) {
            }
        }
        
        return new Response(JSON.stringify(data), {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
        
    } catch (error) {
        return new Response(JSON.stringify({ error: '获取豆瓣推荐失败' }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}
