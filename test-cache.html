<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆瓣缓存测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        button {
            background-color: #e91e63;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #c2185b;
        }
        .result {
            background-color: #2a2a2a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .stats {
            background-color: #1e3a8a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>豆瓣缓存系统测试</h1>
    
    <div class="test-section">
        <h2>API测试</h2>
        <button onclick="testTagsAPI()">测试标签API</button>
        <button onclick="testRecommendAPI()">测试推荐API</button>
        <button onclick="testRecommendAPIAgain()">再次测试推荐API (缓存)</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>缓存管理</h2>
        <button onclick="showCacheStats()">显示缓存统计</button>
        <button onclick="clearCache()">清理缓存</button>
        <div id="cache-stats" class="stats"></div>
    </div>
    
    <div class="test-section">
        <h2>性能测试</h2>
        <button onclick="performanceTest()">性能测试</button>
        <div id="performance-result" class="result"></div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/douban.js"></script>
    <script>
        // 前端缓存配置
        const DOUBAN_CACHE_CONFIG = {
            tags: {
                ttl: 24 * 60 * 60 * 1000, // 1天
                prefix: 'douban_cache_tags_'
            },
            recommend: {
                ttl: 10 * 60 * 1000, // 10分钟
                prefix: 'douban_cache_recommend_'
            }
        };

        // 前端缓存工具函数
        function getCachedData(key) {
            try {
                const cached = localStorage.getItem(key);
                if (!cached) return null;
                
                const data = JSON.parse(cached);
                if (Date.now() > data.expiry) {
                    localStorage.removeItem(key);
                    return null;
                }
                
                return data.value;
            } catch (e) {
                return null;
            }
        }

        function setCachedData(key, value, ttl) {
            try {
                const data = {
                    value: value,
                    expiry: Date.now() + ttl
                };
                localStorage.setItem(key, JSON.stringify(data));
                return true;
            } catch (e) {
                return false;
            }
        }

        // 缓存管理功能
        function clearDoubanCache() {
            try {
                const keys = Object.keys(localStorage);
                let clearedCount = 0;
                
                keys.forEach(key => {
                    if (key.startsWith(DOUBAN_CACHE_CONFIG.tags.prefix) || 
                        key.startsWith(DOUBAN_CACHE_CONFIG.recommend.prefix)) {
                        localStorage.removeItem(key);
                        clearedCount++;
                    }
                });
                
                return clearedCount;
            } catch (e) {
                return 0;
            }
        }

        function getDoubanCacheStats() {
            try {
                const keys = Object.keys(localStorage);
                let tagsCount = 0;
                let recommendCount = 0;
                let totalSize = 0;
                
                keys.forEach(key => {
                    if (key.startsWith(DOUBAN_CACHE_CONFIG.tags.prefix)) {
                        tagsCount++;
                        totalSize += localStorage.getItem(key).length;
                    } else if (key.startsWith(DOUBAN_CACHE_CONFIG.recommend.prefix)) {
                        recommendCount++;
                        totalSize += localStorage.getItem(key).length;
                    }
                });
                
                return {
                    tagsCount,
                    recommendCount,
                    totalSize: Math.round(totalSize / 1024) + 'KB'
                };
            } catch (e) {
                return { tagsCount: 0, recommendCount: 0, totalSize: '0KB' };
            }
        }

        // 测试函数
        async function testTagsAPI() {
            const result = document.getElementById('api-result');
            result.textContent = '正在测试标签API...';

            try {
                const start = Date.now();
                // 直接调用前端缓存函数
                const data = await fetchDoubanTags('movie');
                const end = Date.now();

                result.textContent = `标签API测试成功 (${end - start}ms):\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                result.textContent = `标签API测试失败: ${error.message}`;
            }
        }

        async function testRecommendAPI() {
            const result = document.getElementById('api-result');
            result.textContent = '正在测试推荐API...';

            try {
                const start = Date.now();
                // 直接调用前端缓存函数
                const data = await fetchDoubanRecommend('movie', '热门', 0, 3);
                const end = Date.now();

                result.textContent = `推荐API测试成功 (${end - start}ms):\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                result.textContent = `推荐API测试失败: ${error.message}`;
            }
        }

        async function testRecommendAPIAgain() {
            const result = document.getElementById('api-result');
            result.textContent = '正在再次测试推荐API (应该使用缓存)...';

            try {
                const start = Date.now();
                // 直接调用前端缓存函数
                const data = await fetchDoubanRecommend('movie', '热门', 0, 3);
                const end = Date.now();

                result.textContent = `推荐API再次测试成功 (${end - start}ms):\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                result.textContent = `推荐API再次测试失败: ${error.message}`;
            }
        }

        function showCacheStats() {
            const stats = getDoubanCacheStats();
            const statsDiv = document.getElementById('cache-stats');
            statsDiv.innerHTML = `
                <h3>缓存统计</h3>
                <p>标签缓存: ${stats.tagsCount} 项</p>
                <p>推荐缓存: ${stats.recommendCount} 项</p>
                <p>总大小: ${stats.totalSize}</p>
            `;
        }

        function clearCache() {
            const count = clearDoubanCache();
            const statsDiv = document.getElementById('cache-stats');
            statsDiv.innerHTML = `<p>已清理 ${count} 个缓存项</p>`;
        }

        async function performanceTest() {
            const result = document.getElementById('performance-result');
            result.textContent = '正在进行性能测试...';
            
            try {
                // 清理缓存
                clearDoubanCache();
                
                // 第一次请求（无缓存）
                const start1 = Date.now();
                await fetch('/api/douban/recommend?type=movie&tag=热门&page_start=0&page_limit=5');
                const end1 = Date.now();
                const time1 = end1 - start1;
                
                // 第二次请求（服务端缓存）
                const start2 = Date.now();
                await fetch('/api/douban/recommend?type=movie&tag=热门&page_start=0&page_limit=5');
                const end2 = Date.now();
                const time2 = end2 - start2;
                
                result.textContent = `性能测试结果:
第一次请求 (无缓存): ${time1}ms
第二次请求 (服务端缓存): ${time2}ms
性能提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`;
                
            } catch (error) {
                result.textContent = `性能测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
