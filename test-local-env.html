<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地环境检测测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
    <h1 class="text-2xl font-bold mb-6">本地环境检测测试</h1>
    
    <div class="bg-gray-800 p-6 rounded-lg mb-6">
        <h2 class="text-xl font-semibold mb-4">环境信息</h2>
        <div id="envInfo" class="space-y-2 text-gray-300"></div>
    </div>
    
    <div class="bg-gray-800 p-6 rounded-lg mb-6">
        <h2 class="text-xl font-semibold mb-4">测试结果</h2>
        <div id="testResult" class="space-y-2 text-gray-300"></div>
    </div>
    
    <div class="space-x-4">
        <button onclick="testSearch()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
            测试搜索功能
        </button>
        <button onclick="testDouban()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
            测试豆瓣功能
        </button>
    </div>

    <script src="js/config.js"></script>
    <script src="js/local-mock.js"></script>
    <script>
        // 显示环境信息
        function showEnvInfo() {
            const envInfo = document.getElementById('envInfo');
            envInfo.innerHTML = `
                <div><strong>hostname:</strong> ${window.location.hostname}</div>
                <div><strong>port:</strong> ${window.location.port}</div>
                <div><strong>protocol:</strong> ${window.location.protocol}</div>
                <div><strong>href:</strong> ${window.location.href}</div>
                <div><strong>isLocalEnvironment (config.js):</strong> ${typeof isLocalEnvironment === 'function' ? isLocalEnvironment() : '未定义'}</div>
                <div><strong>window.isLocalEnvironment:</strong> ${typeof window.isLocalEnvironment === 'function' ? window.isLocalEnvironment() : '未定义'}</div>
                <div><strong>mockSearch:</strong> ${typeof window.mockSearch === 'function' ? '已加载' : '未加载'}</div>
                <div><strong>mockDoubanRecommend:</strong> ${typeof window.mockDoubanRecommend === 'function' ? '已加载' : '未加载'}</div>
            `;
        }
        
        // 测试搜索功能
        async function testSearch() {
            const testResult = document.getElementById('testResult');
            testResult.innerHTML = '<div class="text-blue-400">正在测试搜索功能...</div>';
            
            try {
                if (typeof window.mockSearch === 'function') {
                    const result = await window.mockSearch('测试');
                    testResult.innerHTML = `
                        <div class="text-green-400">✓ 搜索功能测试成功</div>
                        <div>找到 ${result.list.length} 个结果</div>
                        <div class="text-sm text-gray-400">${JSON.stringify(result.list[0], null, 2)}</div>
                    `;
                } else {
                    testResult.innerHTML = '<div class="text-red-400">✗ mockSearch 函数未加载</div>';
                }
            } catch (error) {
                testResult.innerHTML = `<div class="text-red-400">✗ 搜索测试失败: ${error.message}</div>`;
            }
        }
        
        // 测试豆瓣功能
        async function testDouban() {
            const testResult = document.getElementById('testResult');
            testResult.innerHTML = '<div class="text-blue-400">正在测试豆瓣功能...</div>';
            
            try {
                if (typeof window.mockDoubanRecommend === 'function') {
                    const result = await window.mockDoubanRecommend();
                    testResult.innerHTML = `
                        <div class="text-green-400">✓ 豆瓣功能测试成功</div>
                        <div>找到 ${result.subjects.length} 个推荐</div>
                        <div class="text-sm text-gray-400">${JSON.stringify(result.subjects[0], null, 2)}</div>
                    `;
                } else {
                    testResult.innerHTML = '<div class="text-red-400">✗ mockDoubanRecommend 函数未加载</div>';
                }
            } catch (error) {
                testResult.innerHTML = `<div class="text-red-400">✗ 豆瓣测试失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载时显示环境信息
        document.addEventListener('DOMContentLoaded', function() {
            showEnvInfo();
        });
    </script>
</body>
</html>
