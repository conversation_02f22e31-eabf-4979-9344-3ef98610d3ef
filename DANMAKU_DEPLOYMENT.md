# 弹幕功能部署说明

## 概述

弹幕功能已经成功集成到 𝒲𝒪𝐵𝒯𝒱 项目中，支持本地文件存储和 Redis 云存储两种模式。

## 功能特性

- ✅ 实时弹幕发送和显示
- ✅ 弹幕数据持久化存储
- ✅ 敏感词过滤
- ✅ 弹幕样式自定义（透明度、字体大小、颜色、速度）
- ✅ 多种显示模式（滚动、顶部、底部）
- ✅ 防重叠算法
- ✅ 发送频率限制

## 存储模式

### 本地开发环境
- 使用本地文件存储
- 弹幕数据保存在 `data/danmaku/` 目录下的 JSON 文件中

### Vercel 生产环境
- 使用 Upstash Redis 云存储
- 通过环境变量自动检测并切换存储模式

## 环境变量配置

在 Vercel 部署时，需要配置以下环境变量（您已经配置完成）：

```
KV_REST_API_URL=https://artistic-vulture-49727.upstash.io
KV_REST_API_TOKEN=AcI_AAIjcDE3MWMzMDkwNjI5YWE0YTdjOWEwNjA4MTE5M2EyNDU0Y3AxMA
KV_REST_API_READ_ONLY_TOKEN=AsI_AAIgcDFOoC6zm6vQJJT5RpRUGhQIwpimQywvTANSeLOyrrm1IA
KV_URL=rediss://default:<EMAIL>:6379
REDIS_URL=rediss://default:<EMAIL>:6379
```

## API 接口

### 获取弹幕
```
GET /api/danmaku/get?id={videoId}
```

### 保存弹幕
```
POST /api/danmaku/save
Content-Type: application/json

{
  "id": "videoId_sourceCode",
  "danmaku": {
    "text": "弹幕内容",
    "time": 123.45,
    "mode": 0,
    "color": "#FFFFFF"
  }
}
```

### 删除弹幕
```
DELETE /api/danmaku/delete
Content-Type: application/json

{
  "id": "videoId_sourceCode",
  "danmakuId": "danmaku-uuid"
}
```

## 部署步骤

1. **推送代码到 GitHub**
   ```bash
   git add .
   git commit -m "添加弹幕功能支持 Redis 存储"
   git push origin main
   ```

2. **Vercel 自动部署**
   - Vercel 会自动检测到代码更新并开始部署
   - 环境变量已经配置，无需额外设置

3. **验证部署**
   - 访问部署后的网站
   - 打开任意视频
   - 测试弹幕发送和显示功能

## 技术实现

### 存储抽象层
- `DanmakuStorage` 类自动检测环境变量
- 有 Redis 配置时使用 Redis 存储
- 无 Redis 配置时使用本地文件存储

### 弹幕插件
- 使用 CDN 版本的 `artplayer-plugin-danmuku`
- 避免了模块加载问题
- 完整的 UI 组件和功能

### 数据格式
```json
{
  "text": "弹幕内容",
  "time": 123.45,
  "mode": 0,
  "color": "#FFFFFF",
  "timestamp": 1640995200000,
  "id": "uuid-string"
}
```

## 使用说明

1. **发送弹幕**
   - 在播放器底部输入框输入内容
   - 点击发送按钮或按回车键

2. **弹幕设置**
   - 点击设置按钮调整透明度、字体大小、速度等
   - 点击样式按钮选择弹幕模式和颜色

3. **弹幕开关**
   - 点击弹幕开关按钮显示/隐藏弹幕

## 注意事项

- 弹幕内容限制 200 字符
- 包含敏感词过滤机制
- 发送频率限制防止刷屏
- Redis 存储在 Vercel 环境自动启用
- 本地开发使用文件存储，无需配置 Redis
