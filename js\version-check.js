// 移除了动画样式，因为不再需要“发现新版”的动画提示

// 获取版本信息
async function fetchVersion(url, errorMessage, options = {}) {
    const response = await fetch(url, options);
    if (!response.ok) {
        throw new Error(errorMessage);
    }
    return await response.text();
}

// 版本检查函数 (只获取当前版本)
async function checkForUpdates() {
    try {
        // 获取当前版本
        const currentVersion = await fetchVersion('/VERSION.txt', '获取当前版本失败', {
            cache: 'no-store' // 确保获取最新本地版本
        });



        // 清理版本字符串（移除可能的空格或换行符）
        const cleanCurrentVersion = currentVersion.trim();

        // 返回版本信息 (只包含当前版本)
        return {
            current: cleanCurrentVersion,
            currentFormatted: formatVersion(cleanCurrentVersion)
        };
    } catch (error) {

        throw error; // 向上抛出错误，由调用者处理
    }
}

// 格式化版本号为可读形式 (yyyyMMddhhmm -> yyyy-MM-dd hh:mm)
function formatVersion(versionString) {
    // 检测版本字符串是否有效
    if (!versionString) {
        return '未知版本';
    }

    // 清理版本字符串（移除可能的空格或换行符）
    const cleanedString = versionString.trim();

    // 格式化标准12位版本号
    if (cleanedString.length === 12) {
        const year = cleanedString.substring(0, 4);
        const month = cleanedString.substring(4, 6);
        const day = cleanedString.substring(6, 8);
        const hour = cleanedString.substring(8, 10);
        const minute = cleanedString.substring(10, 12);

        return `${year}-${month}-${day} ${hour}:${minute}`;
    }

    return cleanedString; // 如果不是12位，则直接返回清理后的字符串
}

//// 创建错误版本信息元素
//function createErrorVersionElement(errorMessage) {
//    const errorElement = document.createElement('p');
//    errorElement.className = 'text-gray-500 text-sm mt-1 text-center md:text-left';
//    errorElement.innerHTML = `版本: <span class="text-amber-500">检测失败</span>`; // 明确设置innerHTML
//    errorElement.title = errorMessage; // 将详细错误信息放在title属性中
//    return errorElement;
//}

// 添加版本信息到页脚
function addVersionInfoToFooter() {
    checkForUpdates().then(result => {
        // 如果checkForUpdates成功返回，但结果不符合预期（例如没有currentFormatted）
        if (!result || !result.currentFormatted) {
            const errorElement = createErrorVersionElement('无法获取当前版本信息');
            displayVersionElement(errorElement);
            return;
        }

        // 创建版本信息元素
        const versionElement = document.createElement('p');
        versionElement.className = 'text-gray-500 text-sm mt-1 text-center md:text-left';

        // 仅显示当前版本，并标记为“最新版本”
        //versionElement.innerHTML = `版本: ${result.currentFormatted} <span class="text-green-500">(最新版本)</span>`;

        // 显示版本元素
        displayVersionElement(versionElement);
    }).catch(error => {

        // 创建错误版本信息元素并显示
        const errorElement = createErrorVersionElement(`版本检测失败: ${error.message}`);
        displayVersionElement(errorElement);
    });
}

// 在页脚显示版本元素的辅助函数
function displayVersionElement(element) {
    // 获取页脚元素
    const footerElement = document.querySelector('.footer p.text-gray-500.text-sm');
    if (footerElement) {
        // 在原版权信息后插入版本信息
        footerElement.insertAdjacentElement('afterend', element);
    } else {
        // 如果找不到页脚元素，尝试在页脚区域最后添加
        const footer = document.querySelector('.footer .container');
        if (footer) {
            // 查找现有的div或直接创建，确保append在正确的层级
            let targetDiv = footer.querySelector('div');
            if (!targetDiv) {
                targetDiv = document.createElement('div');
                footer.appendChild(targetDiv);
            }
            targetDiv.appendChild(element);
        }
    }
}

// 页面加载完成后添加版本信息
document.addEventListener('DOMContentLoaded', addVersionInfoToFooter);