import path from 'path';
import express from 'express';
import axios from 'axios';
import cors from 'cors';
import { fileURLToPath } from 'url';
import fs from 'fs';
import crypto from 'crypto';
import dotenv from 'dotenv';
import { Redis } from '@upstash/redis';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Redis 配置
let redis = null;
if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
  redis = new Redis({
    url: process.env.KV_REST_API_URL,
    token: process.env.KV_REST_API_TOKEN,
  });
}

const config = {
  port: process.env.PORT || 8080,
  password: process.env.PASSWORD || '',
  corsOrigin: process.env.CORS_ORIGIN || '*',
  timeout: parseInt(process.env.REQUEST_TIMEOUT || '5000'),
  maxRetries: parseInt(process.env.MAX_RETRIES || '2'),
  cacheMaxAge: process.env.CACHE_MAX_AGE || '1d',
  userAgent: process.env.USER_AGENT || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  debug: process.env.DEBUG === 'true'
};

const log = (...args) => {
  if (config.debug) {
    // Debug logging removed
  }
};

const app = express();

app.use(cors({
  origin: config.corsOrigin,
  methods: ['GET', 'POST'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});

function sha256Hash(input) {
  return new Promise((resolve) => {
    const hash = crypto.createHash('sha256');
    hash.update(input);
    resolve(hash.digest('hex'));
  });
}

async function renderPage(filePath, password) {
  let content = fs.readFileSync(filePath, 'utf8');
  if (password !== '') {
    const sha256 = await sha256Hash(password);
    content = content.replace('{{PASSWORD}}', sha256);
  }
  return content;
}

app.get(['/', '/index.html', '/player.html'], async (req, res) => {
  try {
    let filePath;
    switch (req.path) {
      case '/player.html':
        filePath = path.join(__dirname, 'player.html');
        break;
      default: // '/' 和 '/index.html'
        filePath = path.join(__dirname, 'index.html');
        break;
    }
    
    const content = await renderPage(filePath, config.password);
    res.send(content);
  } catch (error) {
    res.status(500).send('读取静态页面失败');
  }
});

app.get('/s=:keyword', async (req, res) => {
  try {
    const filePath = path.join(__dirname, 'index.html');
    const content = await renderPage(filePath, config.password);
    res.send(content);
  } catch (error) {
    res.status(500).send('读取静态页面失败');
  }
});

function isValidUrl(urlString) {
  try {
    const parsed = new URL(urlString);
    const allowedProtocols = ['http:', 'https:'];
    
    // 从环境变量获取阻止的主机名列表
    const blockedHostnames = (process.env.BLOCKED_HOSTS || 'localhost,127.0.0.1,0.0.0.0,::1').split(',');
    
    // 从环境变量获取阻止的 IP 前缀
    const blockedPrefixes = (process.env.BLOCKED_IP_PREFIXES || '192.168.,10.,172.').split(',');
    
    if (!allowedProtocols.includes(parsed.protocol)) return false;
    if (blockedHostnames.includes(parsed.hostname)) return false;
    
    for (const prefix of blockedPrefixes) {
      if (parsed.hostname.startsWith(prefix)) return false;
    }
    
    return true;
  } catch {
    return false;
  }
}

// 代理路由
app.get('/proxy/:encodedUrl', async (req, res) => {
  try {
    const encodedUrl = req.params.encodedUrl;
    const targetUrl = decodeURIComponent(encodedUrl);

    // 安全验证
    if (!isValidUrl(targetUrl)) {
      return res.status(400).send('无效的 URL');
    }

    log(`代理请求: ${targetUrl}`);

    // 添加请求超时和重试逻辑
    const maxRetries = config.maxRetries;
    let retries = 0;
    
    const makeRequest = async () => {
      try {
        return await axios({
          method: 'get',
          url: targetUrl,
          responseType: 'stream',
          timeout: config.timeout,
          headers: {
            'User-Agent': config.userAgent
          }
        });
      } catch (error) {
        if (retries < maxRetries) {
          retries++;
          log(`重试请求 (${retries}/${maxRetries}): ${targetUrl}`);
          return makeRequest();
        }
        throw error;
      }
    };

    const response = await makeRequest();

    // 转发响应头（过滤敏感头）
    const headers = { ...response.headers };
    const sensitiveHeaders = (
      process.env.FILTERED_HEADERS || 
      'content-security-policy,cookie,set-cookie,x-frame-options,access-control-allow-origin'
    ).split(',');
    
    sensitiveHeaders.forEach(header => delete headers[header]);
    res.set(headers);

    // 管道传输响应流
    response.data.pipe(res);
  } catch (error) {
    if (error.response) {
      res.status(error.response.status || 500);
      error.response.data.pipe(res);
    } else {
      res.status(500).send(`请求失败: ${error.message}`);
    }
  }
});

// 弹幕数据存储抽象层
class DanmakuStorage {
  constructor() {
    this.useRedis = !!redis;
    if (!this.useRedis) {
      // 本地文件存储目录
      this.danmakuDir = path.join(__dirname, 'data', 'danmaku');
      if (!fs.existsSync(this.danmakuDir)) {
        fs.mkdirSync(this.danmakuDir, { recursive: true });
      }
    }
  }

  async getDanmaku(id) {
    const safeId = id.replace(/[^a-zA-Z0-9_-]/g, '_');

    if (this.useRedis) {
      try {
        const data = await redis.get(`danmaku:${safeId}`);
        return data ? JSON.parse(data) : [];
      } catch (error) {
        return [];
      }
    } else {
      const filePath = path.join(this.danmakuDir, `${safeId}.json`);
      if (!fs.existsSync(filePath)) {
        return [];
      }
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    }
  }

  async saveDanmaku(id, danmakuList) {
    const safeId = id.replace(/[^a-zA-Z0-9_-]/g, '_');

    if (this.useRedis) {
      try {
        await redis.set(`danmaku:${safeId}`, JSON.stringify(danmakuList));
        return true;
      } catch (error) {
        return false;
      }
    } else {
      const filePath = path.join(this.danmakuDir, `${safeId}.json`);
      fs.writeFileSync(filePath, JSON.stringify(danmakuList, null, 2));
      return true;
    }
  }

  async deleteDanmaku(id) {
    const safeId = id.replace(/[^a-zA-Z0-9_-]/g, '_');

    if (this.useRedis) {
      try {
        await redis.del(`danmaku:${safeId}`);
        return true;
      } catch (error) {
        return false;
      }
    } else {
      const filePath = path.join(this.danmakuDir, `${safeId}.json`);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      return true;
    }
  }
}

const danmakuStorage = new DanmakuStorage();


// 豆瓣数据缓存抽象层 - 暂时移除以修复启动问题


// 解析 JSON 请求体
app.use(express.json({ limit: '10mb' }));

// 豆瓣API端点 - 暂时注释掉
/*
// 获取豆瓣标签
app.get('/api/douban/tags', async (req, res) => {
  try {
    const { type } = req.query;

    if (!type || !['movie', 'tv'].includes(type)) {
      return res.status(400).json({ error: '无效的类型参数，必须是movie或tv' });
    }

    const data = await doubanCache.getTags(type);
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: '获取豆瓣标签失败' });
  }
});
*/

/*
// 获取豆瓣推荐内容
app.get('/api/douban/recommend', async (req, res) => {
  try {
    const { type, tag, page_start, page_limit } = req.query;

    if (!type || !['movie', 'tv'].includes(type)) {
      return res.status(400).json({ error: '无效的类型参数，必须是movie或tv' });
    }

    if (!tag) {
      return res.status(400).json({ error: '缺少标签参数' });
    }

    const pageStart = parseInt(page_start) || 0;
    const pageLimit = parseInt(page_limit) || 16;

    // 限制分页参数范围
    if (pageStart < 0 || pageStart > 1000) {
      return res.status(400).json({ error: '无效的页面起始参数' });
    }

    if (pageLimit < 1 || pageLimit > 50) {
      return res.status(400).json({ error: '无效的页面大小参数' });
    }

    const data = await doubanCache.getRecommend(type, tag, pageStart, pageLimit);
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: '获取豆瓣推荐失败' });
  }
});

// 清理豆瓣缓存（可选的管理端点）
app.post('/api/douban/cache/clear', async (req, res) => {
  try {
    const { pattern } = req.body;

    if (!pattern) {
      return res.status(400).json({ error: '缺少清理模式参数' });
    }

    const success = await doubanCache.clearCache(pattern);

    if (success) {
      res.json({ success: true, message: '缓存清理成功' });
    } else {
      res.status(500).json({ error: '缓存清理失败' });
    }
  } catch (error) {
    res.status(500).json({ error: '清理豆瓣缓存失败' });
  }
});
*/

// 获取弹幕数据
app.get('/api/danmaku/get', async (req, res) => {
  try {
    const { id } = req.query;
    if (!id) {
      return res.status(400).json({ error: '缺少视频ID参数' });
    }

    const danmakuList = await danmakuStorage.getDanmaku(id);
    log(`获取弹幕数据: ${id}, 数量: ${danmakuList.length}`);
    res.json(danmakuList);
  } catch (error) {
    res.status(500).json({ error: '获取弹幕数据失败' });
  }
});

// 保存弹幕数据
app.post('/api/danmaku/save', async (req, res) => {
  try {
    const { id, danmaku } = req.body;

    if (!id || !danmaku) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    // 验证弹幕数据格式
    if (!danmaku.text || typeof danmaku.text !== 'string') {
      return res.status(400).json({ error: '弹幕文本无效' });
    }

    if (danmaku.text.length > 200) {
      return res.status(400).json({ error: '弹幕文本过长' });
    }

    // 读取现有数据
    let danmakuList = await danmakuStorage.getDanmaku(id);

    // 添加新弹幕
    const newDanmaku = {
      text: danmaku.text.trim(),
      time: parseFloat(danmaku.time) || 0,
      mode: parseInt(danmaku.mode) || 0,
      color: danmaku.color || '#FFFFFF',
      timestamp: Date.now(),
      id: crypto.randomUUID()
    };

    danmakuList.push(newDanmaku);

    // 保存数据
    const success = await danmakuStorage.saveDanmaku(id, danmakuList);

    if (!success) {
      return res.status(500).json({ error: '保存弹幕数据失败' });
    }

    log(`保存弹幕: ${id}, 内容: ${newDanmaku.text}`);
    res.json({ success: true, danmaku: newDanmaku });
  } catch (error) {
    res.status(500).json({ error: '保存弹幕数据失败' });
  }
});

// 删除弹幕
app.delete('/api/danmaku/delete', async (req, res) => {
  try {
    const { id, danmakuId } = req.body;

    if (!id || !danmakuId) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    let danmakuList = await danmakuStorage.getDanmaku(id);

    const originalLength = danmakuList.length;
    danmakuList = danmakuList.filter(item => item.id !== danmakuId);

    if (danmakuList.length === originalLength) {
      return res.status(404).json({ error: '弹幕不存在' });
    }

    const success = await danmakuStorage.saveDanmaku(id, danmakuList);

    if (!success) {
      return res.status(500).json({ error: '删除弹幕数据失败' });
    }

    log(`删除弹幕: ${id}, ID: ${danmakuId}`);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: '删除弹幕数据失败' });
  }
});

app.use(express.static(path.join(__dirname), {
  maxAge: config.cacheMaxAge
}));

app.use((err, req, res, next) => {
  res.status(500).send('服务器内部错误');
});

app.use((req, res) => {
  res.status(404).send('页面未找到');
});

// 启动服务器
const server = app.listen(config.port, () => {
  if (config.password !== '') {
  }
  if (config.debug) {
  }
});

// 添加错误处理
server.on('error', (err) => {
});

process.on('uncaughtException', (err) => {
});

process.on('unhandledRejection', (reason, promise) => {
});
