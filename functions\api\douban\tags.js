// Cloudflare Workers 豆瓣标签API
export async function onRequest(context) {
    const { request, env } = context;
    const url = new URL(request.url);
    
    // 设置CORS头
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
    };
    
    if (request.method === 'OPTIONS') {
        return new Response(null, { status: 200, headers: corsHeaders });
    }
    
    if (request.method !== 'GET') {
        return new Response(JSON.stringify({ error: '方法不被允许' }), {
            status: 405,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
    
    try {
        const type = url.searchParams.get('type');
        
        if (!type || !['movie', 'tv'].includes(type)) {
            return new Response(JSON.stringify({ error: '无效的类型参数，必须是movie或tv' }), {
                status: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
        }

        const cacheKey = `douban_tags_${type}`;
        const CACHE_TTL = 7 * 24 * 60 * 60; // 7天
        
        // 尝试从KV缓存获取
        let data = null;
        try {
            if (env.LIBRETV_PROXY_KV) {
                const cached = await env.LIBRETV_PROXY_KV.get(cacheKey);
                if (cached) {
                    data = JSON.parse(cached);
                }
            }
        } catch (e) {
        }
        
        if (!data) {
            // 缓存未命中，请求API
            const apiUrl = `https://movie.douban.com/j/search_tags?type=${type}`;
            
            try {
                const response = await fetch(apiUrl, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                        'Referer': 'https://movie.douban.com/',
                        'Accept': 'application/json, text/plain, */*',
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                data = await response.json();
            } catch (err) {
                // 备用方法
                const fallbackUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(apiUrl)}`;
                const fallbackResponse = await fetch(fallbackUrl);
                
                if (!fallbackResponse.ok) {
                    throw new Error(`备用API请求失败! 状态: ${fallbackResponse.status}`);
                }
                
                const fallbackData = await fallbackResponse.json();
                
                if (fallbackData && fallbackData.contents) {
                    data = JSON.parse(fallbackData.contents);
                } else {
                    throw new Error("无法获取有效数据");
                }
            }
            
            // 缓存结果
            try {
                if (env.LIBRETV_PROXY_KV) {
                    await env.LIBRETV_PROXY_KV.put(cacheKey, JSON.stringify(data), {
                        expirationTtl: CACHE_TTL
                    });
                }
            } catch (e) {
            }
        }
        
        return new Response(JSON.stringify(data), {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
        
    } catch (error) {
        return new Response(JSON.stringify({ error: '获取豆瓣标签失败' }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
}
