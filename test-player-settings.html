<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放器设置测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/player.css">
    <script src="libs/hls.min.js"></script>
    <script src="libs/artplayer.min.js"></script>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold mb-6">播放器设置测试</h1>
        
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">测试说明</h2>
            <p class="text-gray-300 mb-4">
                这个页面用于测试播放器的跳过设置功能。点击下方的播放器，然后点击设置按钮查看跳过选项。
            </p>
            <ul class="list-disc list-inside text-gray-300 space-y-1">
                <li>跳过开头：开关控制是否启用，输入框设置跳过时间（10-600秒）</li>
                <li>跳过结尾：开关控制是否启用，输入框设置跳过时间（10-600秒）</li>
                <li>直接在输入框中输入数字即可设置跳过时间</li>
                <li>设置会自动保存到本地存储</li>
            </ul>
        </div>

        <!-- 播放器容器 -->
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">测试播放器</h2>
            <div id="player" style="width: 100%; height: 400px; background: #000;"></div>
        </div>

        <!-- 当前设置显示 -->
        <div class="bg-gray-800 p-6 rounded-lg mt-6">
            <h2 class="text-xl font-semibold mb-4">当前设置</h2>
            <div id="currentSettings" class="text-gray-300">
                <p>跳过开头：<span id="introStatus">未设置</span></p>
                <p>跳过结尾：<span id="outroStatus">未设置</span></p>
            </div>
            <button onclick="updateSettingsDisplay()" class="mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                刷新设置显示
            </button>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script>
        // 跳过片头片尾相关变量
        let skipIntroEnabled = false;
        let skipOutroEnabled = false;
        let skipIntroTime = 90;
        let skipOutroTime = 90;
        let hasSkippedIntro = false;
        let hasSkippedOutro = false;
        let art = null;

        // 从localStorage加载设置
        function loadSettings() {
            skipIntroEnabled = localStorage.getItem('skipIntroEnabled') === 'true';
            skipOutroEnabled = localStorage.getItem('skipOutroEnabled') === 'true';
            skipIntroTime = parseInt(localStorage.getItem('skipIntroTime')) || 90;
            skipOutroTime = parseInt(localStorage.getItem('skipOutroTime')) || 90;
        }

        // 全局函数：更新跳过开头时间
        window.updateSkipIntroTime = function(value) {
            const time = parseInt(value);
            if (!isNaN(time) && time >= 10 && time <= 600) {
                skipIntroTime = time;
                localStorage.setItem('skipIntroTime', skipIntroTime);
                updateSettingsDisplay();
            }
        };

        // 全局函数：更新跳过结尾时间
        window.updateSkipOutroTime = function(value) {
            const time = parseInt(value);
            if (!isNaN(time) && time >= 10 && time <= 600) {
                skipOutroTime = time;
                localStorage.setItem('skipOutroTime', skipOutroTime);
                updateSettingsDisplay();
            }
        };

        // 更新设置显示
        function updateSettingsDisplay() {
            loadSettings();
            document.getElementById('introStatus').textContent = 
                skipIntroEnabled ? `开启 (${skipIntroTime}秒)` : '关闭';
            document.getElementById('outroStatus').textContent = 
                skipOutroEnabled ? `开启 (${skipOutroTime}秒)` : '关闭';
        }

        // 初始化播放器
        function initPlayer() {
            art = new Artplayer({
                container: '#player',
                url: 'https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist.m3u8',
                title: '测试视频',
                volume: 0.8,
                isLive: false,
                muted: false,
                autoplay: false,
                pip: true,
                autoSize: false,
                autoMini: true,
                screenshot: true,
                setting: true,
                settings: [
                    {
                        html: '跳过开头',
                        tooltip: '自动跳过视频开头',
                        switch: skipIntroEnabled,
                        onSwitch: function (item) {
                            skipIntroEnabled = !skipIntroEnabled;
                            localStorage.setItem('skipIntroEnabled', skipIntroEnabled);
                            item.switch = skipIntroEnabled;
                            updateSettingsDisplay();
                            return skipIntroEnabled;
                        }
                    },
                    {
                        html: `<input type="number" id="skipIntroInput" value="${skipIntroTime}" min="10" max="600" style="width:60px;background:#333;color:#fff;border:1px solid #555;border-radius:3px;padding:2px 4px;text-align:center;" onchange="updateSkipIntroTime(this.value)">s`,
                        tooltip: '设置跳过开头的时长（秒）'
                    },
                    {
                        html: '跳过结尾',
                        tooltip: '自动跳过视频结尾',
                        switch: skipOutroEnabled,
                        onSwitch: function (item) {
                            skipOutroEnabled = !skipOutroEnabled;
                            localStorage.setItem('skipOutroEnabled', skipOutroEnabled);
                            item.switch = skipOutroEnabled;
                            updateSettingsDisplay();
                            return skipOutroEnabled;
                        }
                    },
                    {
                        html: `<input type="number" id="skipOutroInput" value="${skipOutroTime}" min="10" max="600" style="width:60px;background:#333;color:#fff;border:1px solid #555;border-radius:3px;padding:2px 4px;text-align:center;" onchange="updateSkipOutroTime(this.value)">s`,
                        tooltip: '设置跳过结尾的时长（秒）'
                    }
                ],
                loop: false,
                flip: false,
                playbackRate: true,
                aspectRatio: false,
                fullscreen: true,
                fullscreenWeb: true,
                subtitleOffset: false,
                miniProgressBar: true,
                mutex: true,
                backdrop: true,
                playsInline: true,
                autoPlayback: false,
                airplay: true,
                hotkey: false,
                theme: '#23ade5',
                lang: navigator.language.toLowerCase()
            });

            // 添加时间更新监听
            art.on('video:timeupdate', function() {
                const currentTime = art.currentTime;
                const duration = art.duration;
                
                // 跳过开头逻辑
                if (skipIntroEnabled && !hasSkippedIntro && currentTime > 3 && currentTime < skipIntroTime) {
                    hasSkippedIntro = true;
                    art.currentTime = skipIntroTime;
                }

                // 跳过结尾逻辑
                if (skipOutroEnabled && !hasSkippedOutro && duration > 0 && currentTime > (duration - skipOutroTime) && currentTime < duration - 5) {
                    hasSkippedOutro = true;
                    art.currentTime = duration - 5;
                }
            });

            art.on('video:loadedmetadata', function() {
                hasSkippedIntro = false;
                hasSkippedOutro = false;
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            updateSettingsDisplay();
            initPlayer();
        });
    </script>
</body>
</html>
