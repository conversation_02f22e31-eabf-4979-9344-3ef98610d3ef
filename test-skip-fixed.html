<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳过功能修复测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/player.css">
    <script src="libs/hls.min.js"></script>
    <script src="libs/artplayer.min.js"></script>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold mb-6">跳过功能修复测试</h1>
        
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">测试说明</h2>
            <div class="text-gray-300 space-y-2">
                <p><strong>修复内容：</strong></p>
                <ul class="list-disc list-inside ml-4 space-y-1">
                    <li>修复了跳过结尾的逻辑问题</li>
                    <li>确保每次新视频加载时重置跳过标志</li>
                    <li>添加了详细的调试日志</li>
                    <li>优化了跳过时机的判断</li>
                </ul>
                <p class="mt-4"><strong>测试方法：</strong></p>
                <ul class="list-disc list-inside ml-4 space-y-1">
                    <li>打开浏览器开发者工具查看控制台日志</li>
                    <li>设置跳过开头为30秒，跳过结尾为30秒</li>
                    <li>播放视频观察跳过效果</li>
                    <li>切换到下一集测试是否重置</li>
                </ul>
            </div>
        </div>

        <!-- 当前设置显示 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">当前设置</h2>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="flex items-center space-x-2 mb-2">
                        <input type="checkbox" id="introEnabled" class="form-checkbox">
                        <span>跳过开头</span>
                    </label>
                    <input type="number" id="introTime" value="30" min="10" max="600" 
                           class="w-20 bg-gray-700 border border-gray-600 rounded px-2 py-1">
                    <span class="ml-1">秒</span>
                </div>
                <div>
                    <label class="flex items-center space-x-2 mb-2">
                        <input type="checkbox" id="outroEnabled" class="form-checkbox">
                        <span>跳过结尾</span>
                    </label>
                    <input type="number" id="outroTime" value="30" min="10" max="600" 
                           class="w-20 bg-gray-700 border border-gray-600 rounded px-2 py-1">
                    <span class="ml-1">秒</span>
                </div>
            </div>
            <button onclick="applySettings()" class="mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                应用设置
            </button>
        </div>

        <!-- 播放器容器 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">测试播放器</h2>
            <div id="player" style="width: 100%; height: 400px; background: #000;"></div>
        </div>

        <!-- 测试视频列表 -->
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">测试视频</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="loadTestVideo(1)" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                    测试视频 1 (120秒)
                </button>
                <button onclick="loadTestVideo(2)" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                    测试视频 2 (180秒)
                </button>
                <button onclick="loadTestVideo(3)" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">
                    测试视频 3 (240秒)
                </button>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script>
        // 跳过相关变量
        let skipIntroEnabled = false;
        let skipOutroEnabled = false;
        let skipIntroTime = 30;
        let skipOutroTime = 30;
        let hasSkippedIntro = false;
        let hasSkippedOutro = false;
        let art = null;

        // 全局函数
        window.updateSkipIntroTime = function(value) {
            const time = parseInt(value);
            if (!isNaN(time) && time >= 10 && time <= 600) {
                skipIntroTime = time;
                localStorage.setItem('skipIntroTime', skipIntroTime);
            }
        };

        window.updateSkipOutroTime = function(value) {
            const time = parseInt(value);
            if (!isNaN(time) && time >= 10 && time <= 600) {
                skipOutroTime = time;
                localStorage.setItem('skipOutroTime', skipOutroTime);
            }
        };

        // 应用设置
        function applySettings() {
            skipIntroEnabled = document.getElementById('introEnabled').checked;
            skipOutroEnabled = document.getElementById('outroEnabled').checked;
            skipIntroTime = parseInt(document.getElementById('introTime').value);
            skipOutroTime = parseInt(document.getElementById('outroTime').value);
            
            localStorage.setItem('skipIntroEnabled', skipIntroEnabled);
            localStorage.setItem('skipOutroEnabled', skipOutroEnabled);
            localStorage.setItem('skipIntroTime', skipIntroTime);
            localStorage.setItem('skipOutroTime', skipOutroTime);
            
            console.log('设置已应用:', { skipIntroEnabled, skipOutroEnabled, skipIntroTime, skipOutroTime });
            
            // 重新初始化播放器设置
            if (art) {
                initPlayerSettings();
            }
        }

        // 加载设置
        function loadSettings() {
            skipIntroEnabled = localStorage.getItem('skipIntroEnabled') === 'true';
            skipOutroEnabled = localStorage.getItem('skipOutroEnabled') === 'true';
            skipIntroTime = parseInt(localStorage.getItem('skipIntroTime')) || 30;
            skipOutroTime = parseInt(localStorage.getItem('skipOutroTime')) || 30;
            
            document.getElementById('introEnabled').checked = skipIntroEnabled;
            document.getElementById('outroEnabled').checked = skipOutroEnabled;
            document.getElementById('introTime').value = skipIntroTime;
            document.getElementById('outroTime').value = skipOutroTime;
        }

        // 初始化播放器设置
        function initPlayerSettings() {
            if (!art) return;
            
            // 这里应该更新播放器的设置面板，但由于ArtPlayer的限制，我们直接在事件中处理
        }

        // 加载测试视频
        function loadTestVideo(videoNum) {
            hasSkippedIntro = false;
            hasSkippedOutro = false;
            
            const testUrls = {
                1: 'https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist.m3u8',
                2: 'https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist.m3u8',
                3: 'https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist.m3u8'
            };
            
            if (art) {
                art.url = testUrls[videoNum];
                art.title = `测试视频 ${videoNum}`;
                console.log(`加载测试视频 ${videoNum}，重置跳过标志`);
            }
        }

        // 初始化播放器
        function initPlayer() {
            art = new Artplayer({
                container: '#player',
                url: 'https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist.m3u8',
                title: '测试视频',
                volume: 0.8,
                isLive: false,
                muted: false,
                autoplay: false,
                pip: true,
                autoSize: false,
                autoMini: true,
                screenshot: true,
                setting: true,
                settings: [
                    {
                        html: '跳过开头',
                        tooltip: '自动跳过视频开头',
                        switch: skipIntroEnabled,
                        onSwitch: function (item) {
                            skipIntroEnabled = !skipIntroEnabled;
                            localStorage.setItem('skipIntroEnabled', skipIntroEnabled);
                            item.switch = skipIntroEnabled;
                            document.getElementById('introEnabled').checked = skipIntroEnabled;
                            return skipIntroEnabled;
                        }
                    },
                    {
                        html: `<input type="number" id="skipIntroInput" value="${skipIntroTime}" min="10" max="600" style="width:60px;background:#333;color:#fff;border:1px solid #555;border-radius:3px;padding:2px 4px;text-align:center;" onchange="updateSkipIntroTime(this.value)">s`,
                        tooltip: '设置跳过开头的时长（秒）'
                    },
                    {
                        html: '跳过结尾',
                        tooltip: '自动跳过视频结尾',
                        switch: skipOutroEnabled,
                        onSwitch: function (item) {
                            skipOutroEnabled = !skipOutroEnabled;
                            localStorage.setItem('skipOutroEnabled', skipOutroEnabled);
                            item.switch = skipOutroEnabled;
                            document.getElementById('outroEnabled').checked = skipOutroEnabled;
                            return skipOutroEnabled;
                        }
                    },
                    {
                        html: `<input type="number" id="skipOutroInput" value="${skipOutroTime}" min="10" max="600" style="width:60px;background:#333;color:#fff;border:1px solid #555;border-radius:3px;padding:2px 4px;text-align:center;" onchange="updateSkipOutroTime(this.value)">s`,
                        tooltip: '设置跳过结尾的时长（秒）'
                    }
                ],
                loop: false,
                flip: false,
                playbackRate: true,
                aspectRatio: false,
                fullscreen: true,
                fullscreenWeb: true,
                subtitleOffset: false,
                miniProgressBar: true,
                mutex: true,
                backdrop: true,
                playsInline: true,
                autoPlayback: false,
                airplay: true,
                hotkey: false,
                theme: '#23ade5',
                lang: navigator.language.toLowerCase()
            });

            // 添加时间更新监听
            art.on('video:timeupdate', function() {
                const currentTime = art.currentTime;
                const duration = art.duration;
                
                // 跳过开头逻辑
                if (skipIntroEnabled && !hasSkippedIntro && currentTime > 3 && currentTime < skipIntroTime) {
                    hasSkippedIntro = true;
                    art.currentTime = skipIntroTime;
                    console.log(`跳过开头: ${currentTime.toFixed(1)}s -> ${skipIntroTime}s`);
                }
                
                // 跳过结尾逻辑
                if (skipOutroEnabled && !hasSkippedOutro && duration > 0) {
                    const skipPoint = duration - skipOutroTime;
                    if (currentTime >= skipPoint && currentTime < duration - 2) {
                        hasSkippedOutro = true;
                        const jumpTo = duration - 2;
                        art.currentTime = jumpTo;
                        console.log(`跳过结尾: ${currentTime.toFixed(1)}s -> ${jumpTo.toFixed(1)}s (总时长: ${duration.toFixed(1)}s)`);
                    }
                }
            });

            art.on('video:loadedmetadata', function() {
                hasSkippedIntro = false;
                hasSkippedOutro = false;
                console.log('视频加载完成，重置跳过标志');
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            initPlayer();
        });
    </script>
</body>
</html>
