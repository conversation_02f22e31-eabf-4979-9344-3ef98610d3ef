# 𝒲𝒪𝐵𝒯𝒱 弹幕系统存储方式与使用指南

## 📋 概述

𝒲𝒪𝐵𝒯𝒱 项目集成了完整的弹幕系统，支持多种存储方式和部署环境。本文档详细说明弹幕系统的存储机制、API接口和使用方法。

## 🏗️ 存储架构

### 存储方式概览

弹幕系统采用**多层存储架构**，根据部署环境自动选择最适合的存储方式：

1. **本地开发环境** - 文件存储
2. **生产环境** - Redis/GitHub Gist存储
3. **前端缓存** - localStorage本地缓存

### 存储抽象层

项目使用 `DanmakuStorage` 类实现存储抽象，自动检测环境并选择合适的存储方式：

```javascript
class DanmakuStorage {
  constructor() {
    this.useRedis = !!redis;  // 检测Redis可用性
    if (!this.useRedis) {
      // 使用本地文件存储
      this.danmakuDir = path.join(__dirname, 'data', 'danmaku');
    }
  }
}
```

## 💾 存储方式详解

### 1. 本地文件存储（开发环境）

**适用场景：** 本地开发、测试环境

**存储位置：** `./data/danmaku/` 目录

**文件命名：** `{videoId}.json`

**特点：**
- ✅ 无需外部依赖
- ✅ 数据持久化
- ✅ 便于调试和查看
- ❌ 不适合生产环境

**数据结构：**
```json
[
  {
    "text": "弹幕内容",
    "time": 123.45,
    "mode": 0,
    "color": "#FFFFFF",
    "timestamp": 1640995200000,
    "id": "uuid-string"
  }
]
```

### 2. Redis存储（生产环境）

**适用场景：** 生产环境，高并发场景

**存储键名：** `danmaku:{safeVideoId}`

**配置要求：**
```bash
# 环境变量配置
KV_REST_API_URL=https://xxx.upstash.io
KV_REST_API_TOKEN=your_token
REDIS_URL=rediss://default:token@host:6379
```

**特点：**
- ✅ 高性能读写
- ✅ 支持高并发
- ✅ 数据持久化
- ✅ 自动过期机制
- ❌ 需要Redis服务

### 3. GitHub Gist存储（备用方案）

**适用场景：** 简化部署，无Redis环境

**存储位置：** GitHub Gist文件

**文件命名：** `danmaku_{safeVideoId}.json`

**配置要求：**
```bash
# 环境变量配置
GITHUB_TOKEN=your_github_token
GIST_ID=your_gist_id
```

**特点：**
- ✅ 免费使用
- ✅ 无需额外服务
- ✅ 版本控制
- ❌ API限制
- ❌ 写入延迟较高

### 4. 前端localStorage缓存

**适用场景：** 所有环境的前端缓存

**存储键名：** `danmaku_{videoId}`

**特点：**
- ✅ 即时响应
- ✅ 离线可用
- ✅ 减少服务器请求
- ❌ 仅本地可见
- ❌ 存储容量限制

## 🔌 API接口详解

### 获取弹幕接口

**端点：** `GET /api/danmaku/get`

**参数：**
- `id` (string, required) - 视频ID

**请求示例：**
```bash
curl "http://localhost:8080/api/danmaku/get?id=54318"
```

**响应格式：**
```json
[
  {
    "text": "精彩的片段！",
    "time": 125.5,
    "mode": 0,
    "color": "#FFFFFF",
    "timestamp": 1640995200000,
    "id": "danmaku-uuid-123"
  }
]
```

### 保存弹幕接口

**端点：** `POST /api/danmaku/save`

**请求头：**
```
Content-Type: application/json
```

**请求体：**
```json
{
  "id": "54318",
  "danmaku": {
    "text": "弹幕内容",
    "time": 123.45,
    "mode": 0,
    "color": "#FFFFFF"
  }
}
```

**响应格式：**
```json
{
  "success": true,
  "message": "弹幕保存成功",
  "danmaku": {
    "text": "弹幕内容",
    "time": 123.45,
    "mode": 0,
    "color": "#FFFFFF",
    "timestamp": 1640995200000,
    "id": "generated-uuid"
  }
}
```

**验证规则：**
- 弹幕文本长度：1-200字符
- 时间：非负数
- 模式：0(滚动)、1(顶部)、2(底部)
- 颜色：十六进制颜色值

## 🎮 前端使用方法

### 弹幕插件集成

项目使用 `artplayer-plugin-danmuku` 插件实现弹幕功能：

```javascript
// 播放器配置
const art = new Artplayer({
  plugins: [
    artplayerPluginDanmuku({
      danmuku: async () => {
        // 获取弹幕数据
        const response = await fetch(`/api/danmaku/get?id=${videoId}`);
        return await response.json();
      },
      beforeEmit: async (danmu) => {
        // 发送弹幕前处理
        const response = await fetch('/api/danmaku/save', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: videoId, danmaku: danmu })
        });
        return response.ok;
      }
    })
  ]
});
```

### 本地缓存机制

前端实现了双重缓存策略：

1. **优先使用localStorage缓存**
2. **异步同步到服务器**
3. **合并本地和服务器数据**

```javascript
// 获取弹幕数据流程
async function getDanmakuData(videoId) {
  // 1. 从localStorage获取本地数据
  const localData = JSON.parse(localStorage.getItem(`danmaku_${videoId}`) || '[]');
  
  // 2. 从服务器获取数据
  const serverData = await fetch(`/api/danmaku/get?id=${videoId}`).then(r => r.json());
  
  // 3. 合并去重
  const allDanmaku = [...localData, ...serverData];
  const uniqueDanmaku = allDanmaku.filter((item, index, self) => 
    index === self.findIndex(d => d.id === item.id)
  );
  
  return uniqueDanmaku;
}
```

## 🚀 部署配置

### 本地开发环境

```bash
# 启动开发服务器
npm run dev

# 弹幕数据将保存在 ./data/danmaku/ 目录
```

### Vercel部署（推荐）

**环境变量配置：**
```bash
# Redis配置（推荐）
KV_REST_API_URL=https://xxx.upstash.io
KV_REST_API_TOKEN=your_token
REDIS_URL=rediss://default:token@host:6379

# 或者GitHub Gist配置（备用）
GITHUB_TOKEN=your_github_token
GIST_ID=your_gist_id
```

### Cloudflare Pages部署

**KV命名空间绑定：**
- 变量名：`WOBTV_PROXY_KV`
- 绑定到KV命名空间实例

### Netlify部署

支持GitHub Gist存储方式，配置相应环境变量即可。

## 🔧 高级配置

### 弹幕过滤和验证

服务器端实现了多层验证：

```javascript
// 内容验证
if (danmaku.text.length > 200) {
  return res.status(400).json({ error: '弹幕文本过长' });
}

// 敏感词过滤（可扩展）
const bannedWords = ['spam', 'ad'];
if (bannedWords.some(word => danmaku.text.includes(word))) {
  return res.status(400).json({ error: '包含禁用词汇' });
}
```

### 性能优化

1. **批量操作** - 支持批量获取和保存弹幕
2. **缓存策略** - 多层缓存减少数据库压力
3. **异步处理** - 弹幕发送不阻塞播放
4. **数据压缩** - 大量弹幕数据压缩存储

### 监控和调试

**调试工具：**
- `test-danmaku.html` - 弹幕API测试页面
- 浏览器控制台 - 查看弹幕加载日志
- 服务器日志 - 监控API调用情况

**测试命令：**
```bash
# 测试获取弹幕
curl "http://localhost:8080/api/danmaku/get?id=test"

# 测试保存弹幕
curl -X POST "http://localhost:8080/api/danmaku/save" \
  -H "Content-Type: application/json" \
  -d '{"id":"test","danmaku":{"text":"测试弹幕","time":10,"mode":0,"color":"#FFFFFF"}}'
```

## 📊 数据格式规范

### 弹幕对象结构

```typescript
interface Danmaku {
  text: string;      // 弹幕文本内容 (1-200字符)
  time: number;      // 出现时间 (秒，非负数)
  mode: number;      // 显示模式 (0:滚动, 1:顶部, 2:底部)
  color: string;     // 颜色值 (十六进制，如 #FFFFFF)
  timestamp: number; // 创建时间戳 (毫秒)
  id: string;        // 唯一标识符 (UUID)
}
```

### 存储键名规范

- **Redis键名：** `danmaku:{safeVideoId}`
- **文件名：** `{safeVideoId}.json`
- **localStorage键名：** `danmaku_{videoId}`
- **Gist文件名：** `danmaku_{safeVideoId}.json`

其中 `safeVideoId` 是经过安全处理的视频ID：
```javascript
const safeId = videoId.replace(/[^a-zA-Z0-9_-]/g, '_');
```

## 🛠️ 故障排除

### 常见问题

**1. 弹幕无法显示**
- 检查API接口是否正常响应
- 确认视频ID参数正确
- 查看浏览器控制台错误信息

**2. 弹幕无法发送**
- 检查网络连接
- 确认服务器存储配置
- 验证弹幕内容格式

**3. 数据丢失**
- 检查存储服务状态
- 确认环境变量配置
- 查看服务器日志

### 调试步骤

1. **检查API响应**
   ```bash
   curl "http://your-domain/api/danmaku/get?id=test"
   ```

2. **验证存储配置**
   - Redis: 检查连接字符串
   - GitHub: 验证Token和Gist ID
   - 本地: 确认目录权限

3. **查看前端日志**
   ```javascript
   // 在浏览器控制台执行
   localStorage.getItem('danmaku_test');
   ```

## 📈 扩展功能

### 计划中的功能

- [ ] 弹幕点赞/举报系统
- [ ] 用户身份验证
- [ ] 弹幕样式自定义
- [ ] 批量管理工具
- [ ] 数据分析统计

### 自定义扩展

项目架构支持轻松扩展新的存储方式和功能：

```javascript
// 扩展新的存储方式
class CustomStorage extends DanmakuStorage {
  async getDanmaku(id) {
    // 实现自定义获取逻辑
  }
  
  async saveDanmaku(id, danmakuList) {
    // 实现自定义保存逻辑
  }
}
```

---

**更新时间：** 2025-01-02  
**版本：** v1.0.0  
**维护者：** 𝒲𝒪𝐵𝒯𝒱 Team
