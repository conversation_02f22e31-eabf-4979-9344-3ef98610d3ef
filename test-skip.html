<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳过片头片尾功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/player.css">
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-6">跳过片头片尾功能测试</h1>
        
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">功能说明</h2>
            <ul class="list-disc list-inside space-y-2">
                <li>在播放器设置中可以开启/关闭跳过片头和片尾功能</li>
                <li>可以自定义跳过片头的时长（30-300秒，默认90秒）</li>
                <li>可以自定义跳过片尾的时长（30-300秒，默认90秒）</li>
                <li>设置会自动保存到本地存储</li>
                <li>播放视频时会自动跳过指定的片头和片尾时间</li>
            </ul>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">测试步骤</h2>
            <ol class="list-decimal list-inside space-y-2">
                <li>点击下方的"测试播放器"按钮</li>
                <li>在播放器中点击设置按钮（齿轮图标）</li>
                <li>在设置面板中找到"跳过片头"和"跳过片尾"选项</li>
                <li>开启相应功能并调整时长</li>
                <li>播放视频测试跳过功能</li>
            </ol>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">测试播放器</h2>
            <a href="player.html?url=https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist.m3u8&title=测试视频&index=0&episodes=[]" 
               class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                测试播放器
            </a>
            <p class="text-gray-400 mt-2 text-sm">
                注意：这是一个示例链接，实际使用时需要有效的视频源
            </p>
        </div>

        <div class="bg-gray-800 p-6 rounded-lg mt-6">
            <h2 class="text-xl font-semibold mb-4">功能验证</h2>
            <div class="space-y-4">
                <div>
                    <h3 class="font-medium mb-2">1. 设置面板验证</h3>
                    <p class="text-gray-400 text-sm">在播放器设置中应该能看到以下选项：</p>
                    <ul class="list-disc list-inside text-gray-400 text-sm ml-4 mt-1">
                        <li>跳过片头（开关）</li>
                        <li>片头时长（滑块，30-300秒）</li>
                        <li>跳过片尾（开关）</li>
                        <li>片尾时长（滑块，30-300秒）</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">2. 功能验证</h3>
                    <p class="text-gray-400 text-sm">开启功能后播放视频时：</p>
                    <ul class="list-disc list-inside text-gray-400 text-sm ml-4 mt-1">
                        <li>视频开始播放5秒后会自动跳过片头时长</li>
                        <li>视频接近结尾时会自动跳过片尾时长</li>
                        <li>跳过时会显示提示信息</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">3. 存储验证</h3>
                    <p class="text-gray-400 text-sm">设置应该持久保存：</p>
                    <ul class="list-disc list-inside text-gray-400 text-sm ml-4 mt-1">
                        <li>刷新页面后设置保持不变</li>
                        <li>重新打开播放器后设置保持不变</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script>
        // 简单的功能验证脚本
        document.addEventListener('DOMContentLoaded', function() {
            console.log('跳过片头片尾功能测试页面已加载');
            
            // 检查localStorage中的设置
            const skipIntroEnabled = localStorage.getItem('skipIntroEnabled');
            const skipOutroEnabled = localStorage.getItem('skipOutroEnabled');
            const skipIntroTime = localStorage.getItem('skipIntroTime');
            const skipOutroTime = localStorage.getItem('skipOutroTime');
            
            console.log('当前设置：', {
                skipIntroEnabled,
                skipOutroEnabled,
                skipIntroTime,
                skipOutroTime
            });
        });
    </script>
</body>
</html>
