// 调试环境变量 API
export default async function handler(req, res) {
  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: '方法不允许' });
  }

  try {
    const envInfo = {
      GITHUB_TOKEN: process.env.GITHUB_TOKEN ? `${process.env.GITHUB_TOKEN.substring(0, 10)}...` : '未设置',
      GIST_ID: process.env.GIST_ID || '未设置',
      KV_REST_API_URL: process.env.KV_REST_API_URL ? '已设置' : '未设置',
      KV_REST_API_TOKEN: process.env.KV_REST_API_TOKEN ? '已设置' : '未设置'
    };

    return res.json(envInfo);
  } catch (error) {
    return res.status(500).json({ error: '调试失败' });
  }
}
