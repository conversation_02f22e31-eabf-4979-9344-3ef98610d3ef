<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳过设置测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
    <h1 class="text-2xl font-bold mb-6">跳过片头片尾设置测试</h1>
    
    <div class="space-y-4 mb-6">
        <div>
            <label class="flex items-center space-x-2">
                <input type="checkbox" id="skipIntroEnabled" class="form-checkbox">
                <span>启用跳过片头</span>
            </label>
        </div>
        
        <div>
            <label class="block">
                <span>片头时长: <span id="introTimeDisplay">90</span>秒</span>
                <input type="range" id="skipIntroTime" min="30" max="300" value="90" step="30" class="w-full mt-1">
            </label>
        </div>
        
        <div>
            <label class="flex items-center space-x-2">
                <input type="checkbox" id="skipOutroEnabled" class="form-checkbox">
                <span>启用跳过片尾</span>
            </label>
        </div>
        
        <div>
            <label class="block">
                <span>片尾时长: <span id="outroTimeDisplay">90</span>秒</span>
                <input type="range" id="skipOutroTime" min="30" max="300" value="90" step="30" class="w-full mt-1">
            </label>
        </div>
    </div>
    
    <div class="space-x-4 mb-6">
        <button id="saveSettings" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">保存设置</button>
        <button id="loadSettings" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">加载设置</button>
        <button id="clearSettings" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded">清除设置</button>
    </div>
    
    <div id="output" class="bg-gray-800 p-4 rounded">
        <h3 class="font-bold mb-2">当前设置:</h3>
        <pre id="settingsDisplay"></pre>
    </div>

    <script>
        // 获取元素
        const skipIntroEnabledEl = document.getElementById('skipIntroEnabled');
        const skipOutroEnabledEl = document.getElementById('skipOutroEnabled');
        const skipIntroTimeEl = document.getElementById('skipIntroTime');
        const skipOutroTimeEl = document.getElementById('skipOutroTime');
        const introTimeDisplayEl = document.getElementById('introTimeDisplay');
        const outroTimeDisplayEl = document.getElementById('outroTimeDisplay');
        const settingsDisplayEl = document.getElementById('settingsDisplay');

        // 更新显示
        function updateDisplay() {
            introTimeDisplayEl.textContent = skipIntroTimeEl.value;
            outroTimeDisplayEl.textContent = skipOutroTimeEl.value;
            
            const settings = {
                skipIntroEnabled: skipIntroEnabledEl.checked,
                skipOutroEnabled: skipOutroEnabledEl.checked,
                skipIntroTime: parseInt(skipIntroTimeEl.value),
                skipOutroTime: parseInt(skipOutroTimeEl.value)
            };
            
            settingsDisplayEl.textContent = JSON.stringify(settings, null, 2);
        }

        // 保存设置
        function saveSettings() {
            localStorage.setItem('skipIntroEnabled', skipIntroEnabledEl.checked);
            localStorage.setItem('skipOutroEnabled', skipOutroEnabledEl.checked);
            localStorage.setItem('skipIntroTime', skipIntroTimeEl.value);
            localStorage.setItem('skipOutroTime', skipOutroTimeEl.value);
            alert('设置已保存');
            updateDisplay();
        }

        // 加载设置
        function loadSettings() {
            skipIntroEnabledEl.checked = localStorage.getItem('skipIntroEnabled') === 'true';
            skipOutroEnabledEl.checked = localStorage.getItem('skipOutroEnabled') === 'true';
            skipIntroTimeEl.value = localStorage.getItem('skipIntroTime') || '90';
            skipOutroTimeEl.value = localStorage.getItem('skipOutroTime') || '90';
            updateDisplay();
        }

        // 清除设置
        function clearSettings() {
            localStorage.removeItem('skipIntroEnabled');
            localStorage.removeItem('skipOutroEnabled');
            localStorage.removeItem('skipIntroTime');
            localStorage.removeItem('skipOutroTime');
            skipIntroEnabledEl.checked = false;
            skipOutroEnabledEl.checked = false;
            skipIntroTimeEl.value = '90';
            skipOutroTimeEl.value = '90';
            alert('设置已清除');
            updateDisplay();
        }

        // 事件监听
        skipIntroTimeEl.addEventListener('input', updateDisplay);
        skipOutroTimeEl.addEventListener('input', updateDisplay);
        skipIntroEnabledEl.addEventListener('change', updateDisplay);
        skipOutroEnabledEl.addEventListener('change', updateDisplay);
        
        document.getElementById('saveSettings').addEventListener('click', saveSettings);
        document.getElementById('loadSettings').addEventListener('click', loadSettings);
        document.getElementById('clearSettings').addEventListener('click', clearSettings);

        // 初始化
        loadSettings();
    </script>
</body>
</html>
