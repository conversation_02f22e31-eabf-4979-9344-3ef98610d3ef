import { Redis } from '@upstash/redis';
import axios from 'axios';

// Redis 配置
let redis = null;
if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
  redis = new Redis({
    url: process.env.KV_REST_API_URL,
    token: process.env.KV_REST_API_TOKEN,
  });
}

const CACHE_TTL = 60 * 60; // 1小时

async function getCache(key) {
  if (redis) {
    try {
      const data = await redis.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      return null;
    }
  }
  return null;
}

async function setCache(key, data, ttl) {
  if (redis) {
    try {
      await redis.setex(key, ttl, JSON.stringify(data));
      return true;
    } catch (error) {
      return false;
    }
  }
  return false;
}

async function fetchDoubanAPI(url) {
  const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36';
  
  try {
    const response = await axios({
      method: 'get',
      url: url,
      timeout: 10000,
      headers: {
        'User-Agent': userAgent,
        'Referer': 'https://movie.douban.com/',
        'Accept': 'application/json, text/plain, */*',
      }
    });
    
    if (response.status !== 200) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    return response.data;
  } catch (err) {
    // 备用方法
    try {
      const fallbackUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
      const fallbackResponse = await axios.get(fallbackUrl, { timeout: 10000 });
      
      if (fallbackResponse.data && fallbackResponse.data.contents) {
        return JSON.parse(fallbackResponse.data.contents);
      } else {
        throw new Error("无法获取有效数据");
      }
    } catch (fallbackErr) {
      throw fallbackErr;
    }
  }
}

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  
  if (req.method !== 'GET') {
    return res.status(405).json({ error: '方法不被允许' });
  }
  
  try {
    const { type, tag, page_start, page_limit } = req.query;
    
    if (!type || !['movie', 'tv'].includes(type)) {
      return res.status(400).json({ error: '无效的类型参数，必须是movie或tv' });
    }
    
    if (!tag) {
      return res.status(400).json({ error: '缺少标签参数' });
    }

    const pageStart = parseInt(page_start) || 0;
    const pageLimit = parseInt(page_limit) || 16;
    
    // 限制分页参数范围
    if (pageStart < 0 || pageStart > 1000) {
      return res.status(400).json({ error: '无效的页面起始参数' });
    }
    
    if (pageLimit < 1 || pageLimit > 50) {
      return res.status(400).json({ error: '无效的页面大小参数' });
    }

    const cacheKey = `douban:recommend:${type}:${tag}:${pageStart}:${pageLimit}`;
    
    // 尝试从缓存获取
    let data = await getCache(cacheKey);
    if (data) {
      return res.json(data);
    }

    // 缓存未命中，请求API
    const url = `https://movie.douban.com/j/search_subjects?type=${type}&tag=${encodeURIComponent(tag)}&sort=recommend&page_limit=${pageLimit}&page_start=${pageStart}`;
    data = await fetchDoubanAPI(url);

    // 缓存结果
    await setCache(cacheKey, data, CACHE_TTL);

    return res.json(data);
  } catch (error) {
    return res.status(500).json({ error: '获取豆瓣推荐失败' });
  }
}
