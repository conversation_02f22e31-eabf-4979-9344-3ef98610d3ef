// 清理弹幕数据 API
import { Redis } from '@upstash/redis';

export default async function handler(req, res) {
  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: '方法不允许' });
  }

  try {
    const { id } = req.body;

    if (!id) {
      return res.status(400).json({ error: '缺少视频ID参数' });
    }

    // 安全处理 ID
    const safeId = id.replace(/[^a-zA-Z0-9_-]/g, '_');

    // 初始化 Redis 连接
    let redis = null;
    if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
      try {
        redis = new Redis({
          url: process.env.KV_REST_API_URL,
          token: process.env.KV_REST_API_TOKEN,
        });
      } catch (error) {
      }
    }

    if (redis) {
      try {
        // 删除 Redis 中的数据
        await redis.del(`danmaku:${safeId}`);
        return res.json({ success: true, message: `已清理视频 ${id} 的弹幕数据` });
      } catch (error) {
        return res.status(500).json({ error: '清理数据失败' });
      }
    } else {
      return res.status(503).json({ error: 'Redis 服务不可用' });
    }
  } catch (error) {
    return res.status(500).json({ error: '清理弹幕数据失败', details: error.message });
  }
}
