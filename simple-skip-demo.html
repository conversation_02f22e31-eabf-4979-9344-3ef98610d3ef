<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能跳过功能</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <div class="bg-gray-800 rounded-lg p-6 shadow-xl">
            <h1 class="text-2xl font-bold text-center mb-6">⏭️ 智能跳过</h1>
            
            <div class="space-y-4">
                <!-- 跳过开头 -->
                <div class="flex items-center justify-between">
                    <span class="text-gray-300">跳过开头</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="skipIntro" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
                
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-400">开头时长</span>
                        <span id="introTime" class="text-blue-400">90s</span>
                    </div>
                    <input type="range" id="introSlider" min="30" max="300" value="90" step="30" class="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer">
                </div>
                
                <!-- 分隔线 -->
                <div class="border-t border-gray-700 my-4"></div>
                
                <!-- 跳过结尾 -->
                <div class="flex items-center justify-between">
                    <span class="text-gray-300">跳过结尾</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="skipOutro" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
                
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-400">结尾时长</span>
                        <span id="outroTime" class="text-blue-400">90s</span>
                    </div>
                    <input type="range" id="outroSlider" min="30" max="300" value="90" step="30" class="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer">
                </div>
            </div>
            
            <div class="mt-6 space-y-3">
                <button id="saveBtn" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                    保存设置
                </button>
                <a href="player.html" class="block w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-lg text-center transition-colors">
                    打开播放器
                </a>
            </div>
            
            <div class="mt-4 text-xs text-gray-500 text-center">
                设置会自动保存到本地
            </div>
        </div>
    </div>

    <script>
        // 获取元素
        const skipIntro = document.getElementById('skipIntro');
        const skipOutro = document.getElementById('skipOutro');
        const introSlider = document.getElementById('introSlider');
        const outroSlider = document.getElementById('outroSlider');
        const introTime = document.getElementById('introTime');
        const outroTime = document.getElementById('outroTime');
        const saveBtn = document.getElementById('saveBtn');

        // 更新显示
        function updateDisplay() {
            introTime.textContent = introSlider.value + 's';
            outroTime.textContent = outroSlider.value + 's';
        }

        // 保存设置
        function saveSettings() {
            localStorage.setItem('skipIntroEnabled', skipIntro.checked);
            localStorage.setItem('skipOutroEnabled', skipOutro.checked);
            localStorage.setItem('skipIntroTime', introSlider.value);
            localStorage.setItem('skipOutroTime', outroSlider.value);
            
            // 显示保存成功
            const originalText = saveBtn.textContent;
            saveBtn.textContent = '已保存 ✓';
            saveBtn.classList.add('bg-green-600');
            saveBtn.classList.remove('bg-blue-600');
            
            setTimeout(() => {
                saveBtn.textContent = originalText;
                saveBtn.classList.remove('bg-green-600');
                saveBtn.classList.add('bg-blue-600');
            }, 1500);
        }

        // 加载设置
        function loadSettings() {
            skipIntro.checked = localStorage.getItem('skipIntroEnabled') === 'true';
            skipOutro.checked = localStorage.getItem('skipOutroEnabled') === 'true';
            introSlider.value = localStorage.getItem('skipIntroTime') || '90';
            outroSlider.value = localStorage.getItem('skipOutroTime') || '90';
            updateDisplay();
        }

        // 事件监听
        introSlider.addEventListener('input', updateDisplay);
        outroSlider.addEventListener('input', updateDisplay);
        skipIntro.addEventListener('change', saveSettings);
        skipOutro.addEventListener('change', saveSettings);
        introSlider.addEventListener('change', saveSettings);
        outroSlider.addEventListener('change', saveSettings);
        saveBtn.addEventListener('click', saveSettings);

        // 初始化
        loadSettings();
    </script>
</body>
</html>
