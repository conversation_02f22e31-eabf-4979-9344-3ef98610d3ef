// 测试 GitHub Gist API
export default async function handler(req, res) {
  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
    const GIST_ID = process.env.GIST_ID;
    
    if (!GITHUB_TOKEN || !GIST_ID) {
      return res.status(500).json({ error: 'GitHub 配置缺失' });
    }

    if (req.method === 'GET') {
      // 读取 Gist
      try {
        const response = await fetch(`https://api.github.com/gists/${GIST_ID}`, {
          headers: {
            'Authorization': `token ${GITHUB_TOKEN}`,
            'Accept': 'application/vnd.github.v3+json'
          }
        });

        const result = await response.json();
        
        return res.json({
          status: response.status,
          ok: response.ok,
          files: Object.keys(result.files || {}),
          error: result.message || null
        });
      } catch (error) {
        return res.status(500).json({ error: error.message });
      }
    }

    if (req.method === 'POST') {
      // 写入测试数据到 Gist
      try {
        const testData = [
          {
            text: "测试弹幕",
            time: 10.5,
            mode: 0,
            color: "#FFFFFF",
            timestamp: Date.now(),
            id: "test-id-123"
          }
        ];

        const response = await fetch(`https://api.github.com/gists/${GIST_ID}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `token ${GITHUB_TOKEN}`,
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            files: {
              'danmaku_test_gist.json': {
                content: JSON.stringify(testData, null, 2)
              }
            }
          })
        });

        const result = await response.json();
        
        return res.json({
          status: response.status,
          ok: response.ok,
          message: response.ok ? '写入成功' : '写入失败',
          error: result.message || null
        });
      } catch (error) {
        return res.status(500).json({ error: error.message });
      }
    }

    return res.status(405).json({ error: '方法不允许' });
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
}
