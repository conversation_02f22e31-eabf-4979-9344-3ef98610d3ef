// 本地测试模拟数据
const MOCK_SEARCH_RESULTS = {
    "code": 200,
    "list": [
        {
            "vod_id": "1001",
            "vod_name": "测试电影1",
            "vod_pic": "https://via.placeholder.com/300x400/333/fff?text=测试电影1",
            "vod_remarks": "HD",
            "vod_year": "2024",
            "vod_area": "中国大陆",
            "vod_director": "测试导演",
            "vod_actor": "测试演员1,测试演员2",
            "vod_content": "这是一个测试电影的简介，用于本地测试功能。",
            "vod_play_url": "测试播放$https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist.m3u8",
            "source_name": "本地测试源",
            "source_code": "local_test"
        },
        {
            "vod_id": "1002", 
            "vod_name": "测试电视剧",
            "vod_pic": "https://via.placeholder.com/300x400/666/fff?text=测试电视剧",
            "vod_remarks": "更新至10集",
            "vod_year": "2024",
            "vod_area": "中国大陆",
            "vod_director": "测试导演2",
            "vod_actor": "测试演员3,测试演员4",
            "vod_content": "这是一个测试电视剧的简介，包含多集内容用于测试播放器功能。",
            "vod_play_url": "第01集$https://vod.bunnycdn.com/test1.m3u8#第02集$https://vod.bunnycdn.com/test2.m3u8#第03集$https://vod.bunnycdn.com/test3.m3u8",
            "source_name": "本地测试源",
            "source_code": "local_test"
        },
        {
            "vod_id": "1003",
            "vod_name": "测试动漫",
            "vod_pic": "https://via.placeholder.com/300x400/999/fff?text=测试动漫",
            "vod_remarks": "完结",
            "vod_year": "2023",
            "vod_area": "日本",
            "vod_director": "测试导演3",
            "vod_actor": "声优1,声优2",
            "vod_content": "这是一个测试动漫的简介，用于测试不同类型的视频内容。",
            "vod_play_url": "第01话$https://vod.bunnycdn.com/anime1.m3u8#第02话$https://vod.bunnycdn.com/anime2.m3u8",
            "source_name": "本地测试源",
            "source_code": "local_test"
        }
    ]
};

// 模拟详情数据
const MOCK_DETAIL_RESULTS = {
    "1001": {
        "code": 200,
        "list": [
            {
                "vod_id": "1001",
                "vod_name": "测试电影1",
                "vod_pic": "https://via.placeholder.com/300x400/333/fff?text=测试电影1",
                "vod_remarks": "HD",
                "vod_year": "2024",
                "vod_area": "中国大陆",
                "vod_director": "测试导演",
                "vod_actor": "测试演员1,测试演员2",
                "vod_content": "这是一个测试电影的详细简介。在本地测试环境中，这个电影可以用来测试播放器的各种功能，包括跳过片头片尾功能。电影时长约120分钟，包含完整的片头和片尾内容。",
                "vod_play_url": "高清播放$https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist.m3u8#标清播放$https://vod.bunnycdn.com/6a6c1b5e-8a5f-4a8b-8e5a-8a5f4a8b8e5a/playlist_low.m3u8",
                "source_name": "本地测试源",
                "source_code": "local_test"
            }
        ]
    },
    "1002": {
        "code": 200,
        "list": [
            {
                "vod_id": "1002",
                "vod_name": "测试电视剧",
                "vod_pic": "https://via.placeholder.com/300x400/666/fff?text=测试电视剧",
                "vod_remarks": "更新至10集",
                "vod_year": "2024",
                "vod_area": "中国大陆",
                "vod_director": "测试导演2",
                "vod_actor": "测试演员3,测试演员4",
                "vod_content": "这是一个测试电视剧的详细简介。该剧共10集，每集约45分钟。可以用来测试播放器的连续播放、自动跳过片头片尾等功能。剧情紧凑，适合测试各种播放场景。",
                "vod_play_url": "第01集$https://vod.bunnycdn.com/test1.m3u8#第02集$https://vod.bunnycdn.com/test2.m3u8#第03集$https://vod.bunnycdn.com/test3.m3u8#第04集$https://vod.bunnycdn.com/test4.m3u8#第05集$https://vod.bunnycdn.com/test5.m3u8#第06集$https://vod.bunnycdn.com/test6.m3u8#第07集$https://vod.bunnycdn.com/test7.m3u8#第08集$https://vod.bunnycdn.com/test8.m3u8#第09集$https://vod.bunnycdn.com/test9.m3u8#第10集$https://vod.bunnycdn.com/test10.m3u8",
                "source_name": "本地测试源",
                "source_code": "local_test"
            }
        ]
    }
};

// 本地环境检测函数已在config.js中定义，这里不再重复定义

// 模拟搜索API
async function mockSearch(query) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    
    // 根据搜索关键词过滤结果
    const filteredResults = MOCK_SEARCH_RESULTS.list.filter(item => 
        item.vod_name.includes(query) || 
        item.vod_actor.includes(query) ||
        item.vod_director.includes(query) ||
        query === '测试' // 特殊关键词返回所有结果
    );
    
    return {
        code: 200,
        list: filteredResults
    };
}

// 模拟详情API
async function mockDetail(id) {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500));
    
    return MOCK_DETAIL_RESULTS[id] || {
        code: 404,
        list: []
    };
}

// 模拟豆瓣推荐数据
const MOCK_DOUBAN_RECOMMEND = {
    "subjects": [
        {
            "id": "1001",
            "title": "测试电影1",
            "cover": "https://via.placeholder.com/300x400/333/fff?text=测试电影1",
            "rate": "8.5",
            "year": "2024",
            "directors": ["测试导演"],
            "casts": ["测试演员1", "测试演员2"],
            "genres": ["剧情", "动作"],
            "regions": ["中国大陆"],
            "url": "https://movie.douban.com/subject/1001/"
        },
        {
            "id": "1002",
            "title": "测试电视剧",
            "cover": "https://via.placeholder.com/300x400/666/fff?text=测试电视剧",
            "rate": "9.0",
            "year": "2024",
            "directors": ["测试导演2"],
            "casts": ["测试演员3", "测试演员4"],
            "genres": ["剧情", "爱情"],
            "regions": ["中国大陆"],
            "url": "https://movie.douban.com/subject/1002/"
        },
        {
            "id": "1003",
            "title": "测试动漫",
            "cover": "https://via.placeholder.com/300x400/999/fff?text=测试动漫",
            "rate": "8.8",
            "year": "2023",
            "directors": ["测试导演3"],
            "casts": ["声优1", "声优2"],
            "genres": ["动画", "冒险"],
            "regions": ["日本"],
            "url": "https://movie.douban.com/subject/1003/"
        }
    ]
};

// 模拟豆瓣推荐API
async function mockDoubanRecommend() {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500));
    return MOCK_DOUBAN_RECOMMEND;
}

// 显示本地测试提示
function showLocalTestNotice() {
    if (isLocalEnvironment()) {
        const notice = document.createElement('div');
        notice.id = 'local-test-notice';
        notice.className = 'fixed top-4 right-4 bg-yellow-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 max-w-sm';
        notice.innerHTML = `
            <div class="flex items-center">
                <span class="mr-2">⚠️</span>
                <div>
                    <div class="font-semibold">本地测试模式</div>
                    <div class="text-sm">使用模拟数据，搜索"测试"查看示例</div>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">×</button>
            </div>
        `;
        document.body.appendChild(notice);

        // 5秒后自动隐藏
        setTimeout(() => {
            if (notice.parentElement) {
                notice.remove();
            }
        }, 8000);
    }
}

// 页面加载时显示提示
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', showLocalTestNotice);
}

// 导出函数供其他模块使用
if (typeof window !== 'undefined') {
    window.mockSearch = mockSearch;
    window.mockDetail = mockDetail;
    window.mockDoubanRecommend = mockDoubanRecommend;
    window.isLocalEnvironment = isLocalEnvironment;
    window.showLocalTestNotice = showLocalTestNotice;
}
