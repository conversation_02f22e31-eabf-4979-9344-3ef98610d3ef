<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹幕 API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #2a2a2a;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        input, textarea {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 3px;
            width: 100%;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>弹幕 API 测试</h1>
    
    <div class="test-section">
        <h3>1. 测试获取弹幕 API</h3>
        <input type="text" id="getVideoId" placeholder="输入视频 ID (例如: 54318)" value="54318">
        <button onclick="testGetDanmaku()">获取弹幕</button>
        <div id="getResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试保存弹幕 API</h3>
        <input type="text" id="saveVideoId" placeholder="输入视频 ID (例如: 54318)" value="54318">
        <textarea id="danmakuText" placeholder="输入弹幕内容" rows="3">测试弹幕内容</textarea>
        <button onclick="testSaveDanmaku()">保存弹幕</button>
        <div id="saveResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 清理弹幕数据</h3>
        <input type="text" id="clearVideoId" placeholder="输入视频 ID" value="54318">
        <button onclick="clearDanmaku()">清理弹幕</button>
        <div id="clearResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 环境信息</h3>
        <button onclick="checkEnvironment()">检查环境</button>
        <div id="envResult" class="result"></div>
    </div>

    <script>
        async function testGetDanmaku() {
            const videoId = document.getElementById('getVideoId').value;
            const resultDiv = document.getElementById('getResult');

            if (!videoId) {
                resultDiv.textContent = '请输入视频 ID';
                return;
            }

            try {
                resultDiv.textContent = '正在获取弹幕...';
                const response = await fetch(`/api/danmaku/get?id=${encodeURIComponent(videoId)}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                let responseText = '';
                try {
                    const data = await response.json();
                    responseText = JSON.stringify(data, null, 2);
                } catch (e) {
                    responseText = await response.text();
                }

                resultDiv.textContent = `状态: ${response.status} ${response.statusText}\n响应: ${responseText}`;
            } catch (error) {
                resultDiv.textContent = `网络错误: ${error.message}`;
            }
        }

        async function testSaveDanmaku() {
            const videoId = document.getElementById('saveVideoId').value;
            const text = document.getElementById('danmakuText').value;
            const resultDiv = document.getElementById('saveResult');

            if (!videoId || !text) {
                resultDiv.textContent = '请输入视频 ID 和弹幕内容';
                return;
            }

            try {
                resultDiv.textContent = '正在保存弹幕...';
                const response = await fetch('/api/danmaku/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        id: videoId,
                        danmaku: {
                            text: text,
                            time: 10.5,
                            mode: 0,
                            color: '#FFFFFF'
                        }
                    })
                });

                let responseText = '';
                try {
                    const data = await response.json();
                    responseText = JSON.stringify(data, null, 2);
                } catch (e) {
                    responseText = await response.text();
                }

                resultDiv.textContent = `状态: ${response.status} ${response.statusText}\n响应: ${responseText}`;
            } catch (error) {
                resultDiv.textContent = `网络错误: ${error.message}`;
            }
        }

        async function clearDanmaku() {
            const videoId = document.getElementById('clearVideoId').value;
            const resultDiv = document.getElementById('clearResult');

            if (!videoId) {
                resultDiv.textContent = '请输入视频 ID';
                return;
            }

            try {
                resultDiv.textContent = '正在清理弹幕数据...';
                const response = await fetch('/api/danmaku/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        id: videoId
                    })
                });

                let responseText = '';
                try {
                    const data = await response.json();
                    responseText = JSON.stringify(data, null, 2);
                } catch (e) {
                    responseText = await response.text();
                }

                resultDiv.textContent = `状态: ${response.status} ${response.statusText}\n响应: ${responseText}`;
            } catch (error) {
                resultDiv.textContent = `网络错误: ${error.message}`;
            }
        }

        function checkEnvironment() {
            const resultDiv = document.getElementById('envResult');
            const info = {
                userAgent: navigator.userAgent,
                location: window.location.href,
                timestamp: new Date().toISOString()
            };
            resultDiv.textContent = JSON.stringify(info, null, 2);
        }

        // 页面加载时自动检查环境
        window.onload = function() {
            checkEnvironment();
        };
    </script>
</body>
</html>
