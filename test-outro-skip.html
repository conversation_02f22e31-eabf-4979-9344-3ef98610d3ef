<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳过结尾功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/player.css">
    <script src="libs/hls.min.js"></script>
    <script src="libs/artplayer.min.js"></script>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold mb-6">跳过结尾功能专项测试</h1>
        
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">测试设置</h2>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block mb-2">跳过结尾时间（秒）:</label>
                    <select id="outroTimeSelect" class="bg-gray-700 border border-gray-600 rounded px-3 py-2 w-full">
                        <option value="10">10秒</option>
                        <option value="20">20秒</option>
                        <option value="30" selected>30秒</option>
                        <option value="60">60秒</option>
                        <option value="90">90秒</option>
                        <option value="120">120秒</option>
                    </select>
                </div>
                <div>
                    <label class="flex items-center space-x-2 mt-6">
                        <input type="checkbox" id="outroEnabled" checked class="form-checkbox">
                        <span>启用跳过结尾</span>
                    </label>
                </div>
            </div>
            <button onclick="applySettings()" class="mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                应用设置并重新加载视频
            </button>
        </div>

        <!-- 播放器容器 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">测试播放器</h2>
            <div id="player" style="width: 100%; height: 400px; background: #000;"></div>
        </div>

        <!-- 状态显示 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">播放状态</h2>
            <div id="statusDisplay" class="text-gray-300 space-y-2">
                <div>当前时间: <span id="currentTime">0</span>s</div>
                <div>总时长: <span id="totalDuration">0</span>s</div>
                <div>跳过点: <span id="skipPoint">0</span>s</div>
                <div>跳过状态: <span id="skipStatus">未跳过</span></div>
                <div>跳过结尾设置: <span id="outroSetting">关闭</span></div>
            </div>
        </div>

        <!-- 测试视频选择 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">测试视频</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="loadTestVideo('main')" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                    怪兽8号第二季
                </button>
                <button onclick="loadTestVideo('test1')" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                    测试视频1
                </button>
                <button onclick="loadTestVideo('test2')" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">
                    测试视频2
                </button>
            </div>
            <p class="text-gray-400 text-sm mt-2">
                使用可以正常播放的视频来验证跳过结尾功能
            </p>
        </div>

        <!-- 测试说明 -->
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">测试说明</h2>
            <div class="text-gray-300 space-y-2">
                <p><strong>测试步骤：</strong></p>
                <ol class="list-decimal list-inside ml-4 space-y-1">
                    <li>选择一个测试视频（建议先用60秒短视频）</li>
                    <li>设置跳过结尾时间（建议10-20秒）</li>
                    <li>确保"启用跳过结尾"已勾选</li>
                    <li>点击"应用设置并重新加载视频"</li>
                    <li>播放视频，观察状态显示</li>
                    <li>当播放接近结尾时，观察是否自动跳过</li>
                </ol>
                <p class="mt-4"><strong>预期行为：</strong></p>
                <ul class="list-disc list-inside ml-4 space-y-1">
                    <li>当播放时间达到"跳过点"时，应该自动跳转到结尾前3秒</li>
                    <li>控制台会输出跳过日志</li>
                    <li>状态显示会更新为"已跳过"</li>
                </ul>
                <p class="mt-4 text-yellow-400"><strong>注意：</strong>如果跳过时间设置过大（超过视频时长的一半），系统会自动调整到合理范围。</p>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script>
        // 跳过相关变量
        let skipOutroEnabled = true;
        let skipOutroTime = 30;
        let hasSkippedOutro = false;
        let art = null;

        // 状态更新
        function updateStatus() {
            if (!art) return;
            
            const currentTime = art.currentTime || 0;
            const duration = art.duration || 0;
            const maxSkipTime = Math.min(skipOutroTime, duration / 2, duration - 10);
            const skipPoint = duration - maxSkipTime;
            
            document.getElementById('currentTime').textContent = currentTime.toFixed(1);
            document.getElementById('totalDuration').textContent = duration.toFixed(1);
            document.getElementById('skipPoint').textContent = skipPoint.toFixed(1);
            document.getElementById('skipStatus').textContent = hasSkippedOutro ? '已跳过' : '未跳过';
            document.getElementById('outroSetting').textContent = skipOutroEnabled ? `开启 (${skipOutroTime}秒)` : '关闭';
        }

        // 加载测试视频
        function loadTestVideo(videoType) {
            hasSkippedOutro = false;

            // 使用可以正常播放的测试视频
            const testUrls = {
                'main': 'https://vip.dytt-cinema.com/20250719/27478_029c9620/index.m3u8',
                'test1': 'https://vip.dytt-cinema.com/20250719/27478_029c9620/index.m3u8',
                'test2': 'https://vip.dytt-cinema.com/20250719/27478_029c9620/index.m3u8'
            };

            const titles = {
                'main': '怪兽8号第二季',
                'test1': '测试视频1',
                'test2': '测试视频2'
            };

            if (art) {
                art.url = testUrls[videoType];
                art.title = titles[videoType];
                console.log(`加载${titles[videoType]}，重置跳过标志`);
            }

            updateStatus();
        }

        // 应用设置
        function applySettings() {
            skipOutroEnabled = document.getElementById('outroEnabled').checked;
            skipOutroTime = parseInt(document.getElementById('outroTimeSelect').value);
            hasSkippedOutro = false;

            localStorage.setItem('skipOutroEnabled', skipOutroEnabled);
            localStorage.setItem('skipOutroTime', skipOutroTime);

            console.log('设置已应用:', { skipOutroEnabled, skipOutroTime });

            // 重新加载视频
            if (art) {
                const currentUrl = art.url;
                art.url = currentUrl;
                hasSkippedOutro = false;
                console.log('视频重新加载，重置跳过状态');
            }

            updateStatus();
        }

        // 全局函数
        window.updateSkipOutroTime = function(value) {
            const time = parseInt(value);
            if (!isNaN(time) && time >= 10 && time <= 600) {
                skipOutroTime = time;
                localStorage.setItem('skipOutroTime', skipOutroTime);
                updateStatus();
            }
        };

        // 初始化播放器
        function initPlayer() {
            art = new Artplayer({
                container: '#player',
                url: 'https://vip.dytt-cinema.com/20250719/27478_029c9620/index.m3u8',
                title: '怪兽8号第二季 - 跳过结尾测试',
                volume: 0.8,
                isLive: false,
                muted: false,
                autoplay: false,
                pip: true,
                autoSize: false,
                autoMini: true,
                screenshot: true,
                setting: true,
                settings: [
                    {
                        html: '跳过结尾',
                        tooltip: '自动跳过视频结尾',
                        switch: skipOutroEnabled,
                        onSwitch: function (item) {
                            skipOutroEnabled = !skipOutroEnabled;
                            localStorage.setItem('skipOutroEnabled', skipOutroEnabled);
                            item.switch = skipOutroEnabled;
                            document.getElementById('outroEnabled').checked = skipOutroEnabled;
                            updateStatus();
                            return skipOutroEnabled;
                        }
                    },
                    {
                        html: `<input type="number" id="skipOutroInput" value="${skipOutroTime}" min="10" max="600" style="width:60px;background:#333;color:#fff;border:1px solid #555;border-radius:3px;padding:2px 4px;text-align:center;" onchange="updateSkipOutroTime(this.value)">s`,
                        tooltip: '设置跳过结尾的时长（秒）'
                    }
                ],
                loop: false,
                flip: false,
                playbackRate: true,
                aspectRatio: false,
                fullscreen: true,
                fullscreenWeb: true,
                subtitleOffset: false,
                miniProgressBar: true,
                mutex: true,
                backdrop: true,
                playsInline: true,
                autoPlayback: false,
                airplay: true,
                hotkey: false,
                theme: '#23ade5',
                lang: navigator.language.toLowerCase()
            });

            // 添加时间更新监听
            art.on('video:timeupdate', function() {
                const currentTime = art.currentTime;
                const duration = art.duration;
                
                // 更新状态显示
                updateStatus();
                
                // 跳过结尾逻辑
                if (skipOutroEnabled && !hasSkippedOutro && duration > 0) {
                    const maxSkipTime = Math.min(skipOutroTime, duration / 2, duration - 10);
                    const skipPoint = duration - maxSkipTime;
                    
                    if (maxSkipTime > 0 && currentTime >= skipPoint && currentTime < duration - 3) {
                        hasSkippedOutro = true;
                        const jumpTo = duration - 3;
                        art.currentTime = jumpTo;
                        updateStatus();
                    }
                }
            });

            art.on('video:loadedmetadata', function() {
                hasSkippedOutro = false;
                console.log('视频加载完成，重置跳过结尾标志');
                updateStatus();
            });

            art.on('ready', function() {
                console.log('播放器准备就绪');
                updateStatus();
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载设置
            skipOutroEnabled = localStorage.getItem('skipOutroEnabled') !== 'false';
            skipOutroTime = parseInt(localStorage.getItem('skipOutroTime')) || 30;
            
            document.getElementById('outroEnabled').checked = skipOutroEnabled;
            document.getElementById('outroTimeSelect').value = skipOutroTime;
            
            initPlayer();
            
            // 定期更新状态
            setInterval(updateStatus, 1000);
        });
    </script>
</body>
</html>
