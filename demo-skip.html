<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳过片头片尾功能演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-6 text-center">⏭️ 智能跳过功能</h1>
        
        <div class="grid md:grid-cols-2 gap-6 mb-8">
            <!-- 功能介绍 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <span class="mr-2">⚡</span>功能特点
                </h2>
                <ul class="space-y-2 text-gray-300">
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">✓</span>
                        <span>跳过开头，30-300秒可调</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">✓</span>
                        <span>跳过结尾，30-300秒可调</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">✓</span>
                        <span>设置自动保存</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">✓</span>
                        <span>简洁提示信息</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">✓</span>
                        <span>每集独立控制</span>
                    </li>
                </ul>
            </div>

            <!-- 使用说明 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <span class="mr-2">📖</span>使用说明
                </h2>
                <ol class="space-y-2 text-gray-300">
                    <li class="flex items-start">
                        <span class="text-blue-400 mr-2 font-bold">1.</span>
                        <span>打开播放器页面</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-400 mr-2 font-bold">2.</span>
                        <span>点击播放器右下角的设置按钮（⚙️）</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-400 mr-2 font-bold">3.</span>
                        <span>在设置面板中找到跳过选项</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-400 mr-2 font-bold">4.</span>
                        <span>开启功能并调整时长</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-400 mr-2 font-bold">5.</span>
                        <span>播放视频享受无缝观看体验</span>
                    </li>
                </ol>
            </div>
        </div>

        <!-- 设置预览 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-8">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <span class="mr-2">⚙️</span>设置预览
            </h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <span>跳过开头</span>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="demoSkipIntro" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    <div class="p-3 bg-gray-700 rounded">
                        <div class="flex justify-between items-center mb-2">
                            <span>开头时长</span>
                            <span id="introTimeValue" class="text-blue-400">90s</span>
                        </div>
                        <input type="range" id="demoIntroTime" min="30" max="300" value="90" step="30" class="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer">
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                        <span>跳过结尾</span>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="demoSkipOutro" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    <div class="p-3 bg-gray-700 rounded">
                        <div class="flex justify-between items-center mb-2">
                            <span>结尾时长</span>
                            <span id="outroTimeValue" class="text-blue-400">90s</span>
                        </div>
                        <input type="range" id="demoOutroTime" min="30" max="300" value="90" step="30" class="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer">
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="text-center">
            <a href="player.html" class="inline-block bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all transform hover:scale-105">
                🎥 打开播放器测试
            </a>
            <p class="text-gray-400 mt-4 text-sm">
                点击上方按钮打开播放器，在设置中启用跳过功能
            </p>
        </div>

        <!-- 技术说明 -->
        <div class="bg-gray-800 p-6 rounded-lg mt-8">
            <h2 class="text-xl font-semibold mb-4 flex items-center">
                <span class="mr-2">🔧</span>技术实现
            </h2>
            <div class="text-gray-300 space-y-2">
                <p>• 基于 ArtPlayer 播放器的自定义设置面板</p>
                <p>• 使用 localStorage 实现设置持久化存储</p>
                <p>• 通过 timeupdate 事件监听播放进度</p>
                <p>• 智能跳过逻辑，避免重复跳转</p>
                <p>• 支持自定义时长范围（30-300秒）</p>
            </div>
        </div>
    </div>

    <script>
        // 演示功能
        const demoIntroTime = document.getElementById('demoIntroTime');
        const demoOutroTime = document.getElementById('demoOutroTime');
        const introTimeValue = document.getElementById('introTimeValue');
        const outroTimeValue = document.getElementById('outroTimeValue');

        demoIntroTime.addEventListener('input', function() {
            introTimeValue.textContent = this.value + 's';
        });

        demoOutroTime.addEventListener('input', function() {
            outroTimeValue.textContent = this.value + 's';
        });

        // 从localStorage加载当前设置
        document.addEventListener('DOMContentLoaded', function() {
            const skipIntroEnabled = localStorage.getItem('skipIntroEnabled') === 'true';
            const skipOutroEnabled = localStorage.getItem('skipOutroEnabled') === 'true';
            const skipIntroTime = localStorage.getItem('skipIntroTime') || '90';
            const skipOutroTime = localStorage.getItem('skipOutroTime') || '90';

            document.getElementById('demoSkipIntro').checked = skipIntroEnabled;
            document.getElementById('demoSkipOutro').checked = skipOutroEnabled;
            demoIntroTime.value = skipIntroTime;
            demoOutroTime.value = skipOutroTime;
            introTimeValue.textContent = skipIntroTime + 's';
            outroTimeValue.textContent = skipOutroTime + 's';
        });
    </script>
</body>
</html>
