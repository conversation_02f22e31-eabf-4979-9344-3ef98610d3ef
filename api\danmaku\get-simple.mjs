// 简化版弹幕获取 API - 使用 GitHub Gist 作为存储
export default async function handler(req, res) {
  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: '方法不允许' });
  }

  try {
    const { id } = req.query;

    
    if (!id) {
      return res.status(400).json({ error: '缺少视频ID参数' });
    }

    // GitHub Gist 配置
    const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
    const GIST_ID = process.env.GIST_ID;
    
    if (!GITHUB_TOKEN || !GIST_ID) {

      return res.json([]);
    }

    const safeId = id.replace(/[^a-zA-Z0-9_-]/g, '_');
    const filename = `danmaku_${safeId}.json`;

    try {
      // 从 GitHub Gist 获取数据
      const response = await fetch(`https://api.github.com/gists/${GIST_ID}`, {
        headers: {
          'Authorization': `token ${GITHUB_TOKEN}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });

      if (response.ok) {
        const gist = await response.json();
        const file = gist.files[filename];
        
        if (file && file.content) {
          const danmakuList = JSON.parse(file.content);

          return res.json(danmakuList);
        }
      }
    } catch (error) {

    }

    // 如果获取失败，返回空数组

    return res.json([]);
    
  } catch (error) {

    return res.status(500).json({ error: '获取弹幕数据失败', details: error.message });
  }
}
