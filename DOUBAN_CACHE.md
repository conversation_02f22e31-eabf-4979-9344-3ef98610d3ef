# 豆瓣API缓存系统

本项目已集成豆瓣API缓存系统，用于减少对豆瓣API的请求次数，避免因请求过多而导致的错误。

## 缓存架构

### 两层缓存设计
1. **服务端缓存**：使用Redis或KV存储，在服务器端缓存豆瓣API响应
2. **前端缓存**：使用localStorage，在浏览器端缓存最近的请求结果

### 缓存策略
- **标签数据**：服务端缓存7天，前端缓存1天（标签变化很少）
- **推荐内容**：服务端缓存1小时，前端缓存10分钟（需要一定新鲜度）

## 部署平台支持

### Vercel
- 使用Upstash Redis作为缓存存储
- 需要配置环境变量：
  - `KV_REST_API_URL`：Redis连接URL
  - `KV_REST_API_TOKEN`：Redis访问令牌

### Cloudflare Pages
- 使用Cloudflare KV作为缓存存储
- 需要绑定KV命名空间：`LIBRETV_PROXY_KV`

### Netlify
- 暂时直接调用API（可扩展外部缓存服务）
- 保留降级机制和备用API

## API端点

### 获取标签
```
GET /api/douban/tags?type=movie|tv
```

### 获取推荐内容
```
GET /api/douban/recommend?type=movie|tv&tag=标签&page_start=0&page_limit=16
```

## 缓存管理

### 前端缓存管理
在浏览器控制台中可以使用以下命令：

```javascript
// 查看缓存统计
doubanCacheManager.stats()

// 清理所有缓存
doubanCacheManager.clearAll()
```

### 服务端缓存管理
- 缓存会自动过期
- 可以通过重启服务清理内存缓存
- Redis/KV缓存支持TTL自动过期

## 降级机制

1. **前端降级**：如果本地API失败，自动回退到直接豆瓣API调用
2. **服务端降级**：如果Redis不可用，直接调用豆瓣API
3. **备用API**：如果豆瓣API失败，使用allorigins.win作为备用代理

## 性能优化

### 缓存命中率
- 标签数据：预期90%+命中率
- 推荐内容：预期70%+命中率（取决于用户行为）

### 请求减少
- 理论上可减少80%+的豆瓣API请求
- 显著提升页面加载速度
- 减少因频繁请求导致的限流风险

## 监控和调试

### 日志输出
- 缓存命中/未命中日志
- API请求成功/失败日志
- 错误和降级日志

### 测试页面
访问 `/test-cache.html` 可以测试缓存功能和查看性能数据。

## 注意事项

1. **环境变量**：确保正确配置Redis连接信息
2. **KV绑定**：Cloudflare需要正确绑定KV命名空间
3. **缓存清理**：定期监控缓存使用情况
4. **降级测试**：定期测试降级机制是否正常工作

## 故障排除

### 常见问题
1. **缓存不生效**：检查Redis连接或KV绑定
2. **API请求失败**：检查网络连接和代理设置
3. **前端缓存过期**：检查localStorage容量限制

### 调试步骤
1. 检查浏览器控制台日志
2. 查看服务器端日志
3. 使用测试页面验证功能
4. 检查缓存统计信息
