// Netlify Functions 豆瓣标签API
import fetch from 'node-fetch';

export const handler = async (event, context) => {
    // 设置CORS头
    const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
    };
    
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers: corsHeaders,
            body: ''
        };
    }
    
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            body: JSON.stringify({ error: '方法不被允许' })
        };
    }
    
    try {
        const { type } = event.queryStringParameters || {};
        
        if (!type || !['movie', 'tv'].includes(type)) {
            return {
                statusCode: 400,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                body: JSON.stringify({ error: '无效的类型参数，必须是movie或tv' })
            };
        }

        // Netlify没有内置KV存储，这里直接请求API
        // 可以考虑使用外部缓存服务如Redis或者简单的内存缓存
        
        const apiUrl = `https://movie.douban.com/j/search_tags?type=${type}`;
        let data;
        
        try {
            const response = await fetch(apiUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://movie.douban.com/',
                    'Accept': 'application/json, text/plain, */*',
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            
            data = await response.json();
        } catch (err) {
            // 备用方法
            const fallbackUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(apiUrl)}`;
            const fallbackResponse = await fetch(fallbackUrl);
            
            if (!fallbackResponse.ok) {
                throw new Error(`备用API请求失败! 状态: ${fallbackResponse.status}`);
            }
            
            const fallbackData = await fallbackResponse.json();
            
            if (fallbackData && fallbackData.contents) {
                data = JSON.parse(fallbackData.contents);
            } else {
                throw new Error("无法获取有效数据");
            }
        }
        

        
        return {
            statusCode: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        };
        
    } catch (error) {

        return {
            statusCode: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            body: JSON.stringify({ error: '获取豆瓣标签失败' })
        };
    }
};
