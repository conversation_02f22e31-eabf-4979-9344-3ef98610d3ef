<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳过功能验证</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
    <h1 class="text-2xl font-bold mb-6">跳过功能验证</h1>
    
    <div class="bg-gray-800 p-6 rounded-lg mb-6">
        <h2 class="text-xl font-semibold mb-4">功能测试</h2>
        
        <div class="space-y-4">
            <!-- 跳过开头测试 -->
            <div class="flex items-center space-x-4">
                <span class="w-20">跳过开头:</span>
                <label class="flex items-center">
                    <input type="checkbox" id="introSwitch" class="mr-2">
                    <span>启用</span>
                </label>
                <input type="number" id="introTime" value="90" min="10" max="600" 
                       class="w-20 bg-gray-700 border border-gray-600 rounded px-2 py-1 text-center">
                <span>秒</span>
            </div>
            
            <!-- 跳过结尾测试 -->
            <div class="flex items-center space-x-4">
                <span class="w-20">跳过结尾:</span>
                <label class="flex items-center">
                    <input type="checkbox" id="outroSwitch" class="mr-2">
                    <span>启用</span>
                </label>
                <input type="number" id="outroTime" value="90" min="10" max="600" 
                       class="w-20 bg-gray-700 border border-gray-600 rounded px-2 py-1 text-center">
                <span>秒</span>
            </div>
        </div>
        
        <div class="mt-6 space-x-4">
            <button onclick="saveSettings()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                保存设置
            </button>
            <button onclick="loadSettings()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                加载设置
            </button>
            <button onclick="testSettings()" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">
                测试设置
            </button>
        </div>
    </div>
    
    <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">测试结果</h2>
        <div id="testResult" class="text-gray-300">
            点击"测试设置"按钮查看当前配置
        </div>
    </div>

    <script>
        // 保存设置到localStorage
        function saveSettings() {
            const introEnabled = document.getElementById('introSwitch').checked;
            const outroEnabled = document.getElementById('outroSwitch').checked;
            const introTime = parseInt(document.getElementById('introTime').value);
            const outroTime = parseInt(document.getElementById('outroTime').value);
            
            localStorage.setItem('skipIntroEnabled', introEnabled);
            localStorage.setItem('skipOutroEnabled', outroEnabled);
            localStorage.setItem('skipIntroTime', introTime);
            localStorage.setItem('skipOutroTime', outroTime);
            
            document.getElementById('testResult').innerHTML = `
                <div class="text-green-400">✓ 设置已保存</div>
                <div>跳过开头: ${introEnabled ? '启用' : '关闭'} (${introTime}秒)</div>
                <div>跳过结尾: ${outroEnabled ? '启用' : '关闭'} (${outroTime}秒)</div>
            `;
        }
        
        // 从localStorage加载设置
        function loadSettings() {
            const introEnabled = localStorage.getItem('skipIntroEnabled') === 'true';
            const outroEnabled = localStorage.getItem('skipOutroEnabled') === 'true';
            const introTime = parseInt(localStorage.getItem('skipIntroTime')) || 90;
            const outroTime = parseInt(localStorage.getItem('skipOutroTime')) || 90;
            
            document.getElementById('introSwitch').checked = introEnabled;
            document.getElementById('outroSwitch').checked = outroEnabled;
            document.getElementById('introTime').value = introTime;
            document.getElementById('outroTime').value = outroTime;
            
            document.getElementById('testResult').innerHTML = `
                <div class="text-blue-400">✓ 设置已加载</div>
                <div>跳过开头: ${introEnabled ? '启用' : '关闭'} (${introTime}秒)</div>
                <div>跳过结尾: ${outroEnabled ? '启用' : '关闭'} (${outroTime}秒)</div>
            `;
        }
        
        // 测试当前设置
        function testSettings() {
            const introEnabled = document.getElementById('introSwitch').checked;
            const outroEnabled = document.getElementById('outroSwitch').checked;
            const introTime = parseInt(document.getElementById('introTime').value);
            const outroTime = parseInt(document.getElementById('outroTime').value);
            
            // 验证设置
            let errors = [];
            if (introTime < 10 || introTime > 600) {
                errors.push('开头时间应在10-600秒之间');
            }
            if (outroTime < 10 || outroTime > 600) {
                errors.push('结尾时间应在10-600秒之间');
            }
            
            if (errors.length > 0) {
                document.getElementById('testResult').innerHTML = `
                    <div class="text-red-400">✗ 设置有误</div>
                    ${errors.map(err => `<div class="text-red-300">- ${err}</div>`).join('')}
                `;
            } else {
                document.getElementById('testResult').innerHTML = `
                    <div class="text-green-400">✓ 设置正确</div>
                    <div>跳过开头: ${introEnabled ? '启用' : '关闭'} (${introTime}秒)</div>
                    <div>跳过结尾: ${outroEnabled ? '启用' : '关闭'} (${outroTime}秒)</div>
                    <div class="mt-2 text-gray-400">
                        ${introEnabled ? `视频播放3秒后会跳转到第${introTime}秒` : '不会跳过开头'}
                    </div>
                    <div class="text-gray-400">
                        ${outroEnabled ? `视频结束前${outroTime}秒会跳转到结束前5秒` : '不会跳过结尾'}
                    </div>
                `;
            }
        }
        
        // 页面加载时自动加载设置
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
        });
        
        // 监听输入变化
        document.getElementById('introTime').addEventListener('change', function() {
            if (this.value < 10) this.value = 10;
            if (this.value > 600) this.value = 600;
        });
        
        document.getElementById('outroTime').addEventListener('change', function() {
            if (this.value < 10) this.value = 10;
            if (this.value > 600) this.value = 600;
        });
    </script>
</body>
</html>
