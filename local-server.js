#!/usr/bin/env node

/**
 * 本地开发服务器 - 支持代理功能
 * 使用方法：node local-server.js
 * 然后访问 http://localhost:3000
 */

const http = require('http');
const https = require('https');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

// 获取文件的MIME类型
function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// 代理请求函数
function proxyRequest(targetUrl, res) {
    const urlObj = new URL(targetUrl);
    const isHttps = urlObj.protocol === 'https:';
    const httpModule = isHttps ? https : http;
    
    const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': urlObj.origin
        },
        timeout: 10000
    };

    const proxyReq = httpModule.request(options, (proxyRes) => {
        // 设置CORS头
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', '*');
        
        // 转发状态码和头部
        res.writeHead(proxyRes.statusCode, proxyRes.headers);
        
        // 转发响应数据
        proxyRes.pipe(res);
    });

    proxyReq.on('error', (err) => {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end(`代理请求失败: ${err.message}`);
    });

    proxyReq.on('timeout', () => {
        proxyReq.destroy();
        res.writeHead(504, { 'Content-Type': 'text/plain' });
        res.end('代理请求超时');
    });

    proxyReq.end();
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;

    // 处理CORS预检请求
    if (req.method === 'OPTIONS') {
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', '*');
        res.writeHead(204);
        res.end();
        return;
    }

    // 处理代理请求
    if (pathname.startsWith('/proxy/')) {
        const encodedUrl = pathname.substring(7); // 移除 '/proxy/'
        try {
            const targetUrl = decodeURIComponent(encodedUrl);
            proxyRequest(targetUrl, res);
        } catch (err) {
            res.writeHead(400, { 'Content-Type': 'text/plain' });
            res.end('无效的URL');
        }
        return;
    }

    // 处理静态文件
    let filePath = path.join(__dirname, pathname === '/' ? 'index.html' : pathname);
    
    // 安全检查：防止目录遍历攻击
    if (!filePath.startsWith(__dirname)) {
        res.writeHead(403, { 'Content-Type': 'text/plain' });
        res.end('禁止访问');
        return;
    }

    fs.stat(filePath, (err, stats) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('文件未找到');
            return;
        }

        if (stats.isDirectory()) {
            filePath = path.join(filePath, 'index.html');
        }

        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(404, { 'Content-Type': 'text/plain' });
                res.end('文件未找到');
                return;
            }

            const contentType = getContentType(filePath);
            res.setHeader('Content-Type', contentType);
            
            // 设置缓存头
            if (contentType.startsWith('image/') || contentType.includes('font')) {
                res.setHeader('Cache-Control', 'public, max-age=86400'); // 1天
            } else {
                res.setHeader('Cache-Control', 'no-cache');
            }
            
            res.writeHead(200);
            res.end(data);
        });
    });
});

// 启动服务器
server.listen(PORT, () => {




});

// 优雅关闭
process.on('SIGINT', () => {

    server.close(() => {

        process.exit(0);
    });
});

process.on('SIGTERM', () => {

    server.close(() => {

        process.exit(0);
    });
});
